import type { PlanSubmitData } from '@/views/planSubscription/composables/usePlanForm'
import { palette, updatePrimaryPalette } from '@primeuix/themes'
import { defineStore } from 'pinia'
import { reactive, ref } from 'vue'
import { customer as customerApi } from '@/services/api'
import { pickColor } from '@/views/communication/theme'
import { apiTransform } from '@/views/planSubscription/composables/usePlanForm'

interface CustomerInfo {
  customer_id: string
  customer_name: string
  customer_email: string
  customer_phone: string
  return_url: string
  customer_plan_bankings?: Api.CreateCustomerInviteBank
  customer_plan?: PlanSubmitData | Record<string, any>
  customer_bankings?: Api.CreateCustomerInviteBank[]
  theme: string
}

interface BankAccountDetails {
  companyName: string
  firstName: string
  lastName: string
  addressLine1: string
  addressLine2: string
  city: string
  state: string
  postcode: string
  email: string
  bsbNumber: string
  accountNumber: string
  bankAccountName: string
}

interface PaymentDetails {
  cardNumber: string
  expiryDate: string
  securityCode: string
  nameOnCard: string
  email: string
}

interface BillingAddress {
  country: string
  first_name: string
  last_name: string
  company: string
  address_line1: string
  address_line2: string
  city: string
  state: string
  postal_code: string
  phone: string
}

export const useCheckoutStore = defineStore('checkout', () => {
  const activePaymentMethod = ref<'card' | 'bankAccount'>('card')
  const selectedCurrency = ref('AUD')
  const pageStatus = ref<'loading' | 'form' | 'success' | 'error'>('form')
  const pageErrorMessage = ref('')
  const customerId = ref('')
  const redirectCountdown = ref(10)
  const cssVariables = ref<Record<string, string>>({})

  const config = ref<Api.GetCustomerInviteRes & { headerStyle: { backgroundColor: string, color: string } }>({
    customer_email: '',
    customer_name: '',
    logo: '',
    phone_number: '',
    theme: '',
    merchant_name: '',
    is_payment_method: false,
    open_banking_transactions: false,
    headerStyle: {
      backgroundColor: '',
      color: '',
    },
  })

  // 主题样式
  const themeStyles = ref<Record<string, string>>({})
  const customerInfo = reactive<CustomerInfo>({
    customer_id: '',
    customer_name: '',
    customer_email: '',
    customer_phone: '',
    return_url: location.href,
    theme: '',
    customer_plan: {
      surcharge_rate: {
        /**
         * 类型，1-百分比 2-固定值
         */
        fee_rate: '2',
        /**
         * 费率
         */
        fee_value: '0',
      },
    },
    customer_bankings: [],
  })

  const selectedBank = ref<number | null>(null)

  const isConfirmPaymentMethod = ref(false)

  const isSelectedBankLoading = ref(false)

  const bankAccountDetails = reactive<BankAccountDetails>({
    companyName: '',
    firstName: '',
    lastName: '',
    addressLine1: '',
    addressLine2: '',
    city: '',
    state: '',
    postcode: '',
    email: '',
    bsbNumber: '',
    accountNumber: '',
    bankAccountName: '',
  })

  const paymentDetails = reactive<PaymentDetails>({
    cardNumber: '',
    expiryDate: '',
    securityCode: '',
    nameOnCard: '',
    email: '',
  })

  const billingAddress = reactive<BillingAddress>({
    country: '',
    first_name: '',
    last_name: '',
    company: '',
    address_line1: '',
    address_line2: '',
    city: '',
    state: '',
    postal_code: '',
    phone: '',
  })

  // Actions
  function setCustomerInfo(info: Partial<CustomerInfo>) {
    Object.assign(customerInfo, info)
  }

  function setBankAccountDetails(details: Partial<BankAccountDetails>) {
    Object.assign(bankAccountDetails, details)
  }

  function setPaymentDetails(details: Partial<PaymentDetails>) {
    Object.assign(paymentDetails, details)
  }

  function setBillingAddress(address: Partial<BillingAddress>) {
    Object.assign(billingAddress, address)
  }

  function setPageStatus(status: 'loading' | 'form' | 'success' | 'error') {
    pageStatus.value = status
    if (status === 'success') {
      redirectToOrigin()
    }
  }

  function redirectToOrigin() {
    redirectCountdown.value = 10
    const countdownInterval = setInterval(() => {
      redirectCountdown.value--
      if (redirectCountdown.value <= 0) {
        clearInterval(countdownInterval)
      }
    }, 1000)

    setTimeout(() => {
      clearInterval(countdownInterval)
      window.location.href = location.origin
    }, 10000)
  }

  function setPageErrorMessage(message: string) {
    pageErrorMessage.value = message
  }

  function setActivePaymentMethod(method: 'card' | 'bankAccount') {
    activePaymentMethod.value = method
  }

  function setSelectedCurrency(currency: string) {
    selectedCurrency.value = currency
  }

  function setCustomerId(id: string) {
    customerId.value = id
    if (customerId.value !== id) {
      resetForm()
    }
    updateConfig(id)
  }

  async function updateConfig(customer_id: string) {
    const { data, code, message } = await customerApi.getInviteConfig({
      customer_id,
    })
    if (code === 0) {
      Object.assign(config.value, data)
      if (config.value.open_banking_transactions) {
        activePaymentMethod.value = 'bankAccount'
      }
      else {
        activePaymentMethod.value = 'card'
      }
      if (data.customer_plan) {
        customerInfo.customer_plan = apiTransform(data.customer_plan, customerInfo.customer_plan as PlanSubmitData)
      }
      if (!customerInfo.customer_name) {
        customerInfo.customer_name = data.customer_name
      }
      if (!customerInfo.customer_email) {
        customerInfo.customer_email = data.customer_email
      }
      if (!customerInfo.customer_phone) {
        customerInfo.customer_phone = data.phone_number
      }
      if (data.customer_bankings && Array.isArray(data.customer_bankings) && data.customer_bankings.length > 0) {
        const id = data?.customer_bankings?.find(item => item.id === Number(data?.customer_plan_bankings?.id))?.id
        customerInfo.customer_bankings = data.customer_bankings?.map(item => ({
          ...item,
          id: Number(item.id),
        }))
        if (id) {
          selectedBank.value = Number(id)
        }
      }
      config.value.headerStyle = pickColor(config.value.theme) as { backgroundColor: string, color: string }
      changeTheme(pickColor(config.value.theme).backgroundColor || '6466f1')
    }
    else {
      setPageStatus('error')
      setPageErrorMessage(message || 'Submission failed, please try again')
    }
  }

  async function confirmPaymentMethod() {
    if (!selectedBank.value) {
      window.$toast.add({
        severity: 'error',
        summary: 'Error',
        detail: 'Please select a payment method',
      })
      return
    }
    try {
      isSelectedBankLoading.value = true
      const { code } = await customerApi.submitInvite({
        customer_id: customerId.value,
        payment_method_id: selectedBank.value as number,
        customer_email: customerInfo.customer_email,
        customer_name: customerInfo.customer_name,
        phone_number: customerInfo.customer_phone,
        currency: selectedCurrency.value,
      } as Api.CreateCustomerInviteChangePaymentMethodReq)

      if (code === 0) {
        pageStatus.value = 'success'
        pageErrorMessage.value = ''
        redirectToOrigin()
      }
      else {
        setPageStatus('error')
        setPageErrorMessage('Submission failed, please try again')
      }
    }
    catch (error) {
      setPageStatus('error')
      setPageErrorMessage(error instanceof Error ? error.message : 'Submission failed, please try again')
    }
    finally {
      isSelectedBankLoading.value = false
    }
  }

  function cancelPaymentMethod() {
    isConfirmPaymentMethod.value = true
  }

  function changeTheme(theme: string) {
    const primaryColors: Record<number, string> = palette(theme || '#6466f1')
    Object.keys(primaryColors).forEach((key) => {
      themeStyles.value[`--p-primary-${key}`] = primaryColors[Number(key)]
    })

    cssVariables.value = {
      '--p-primary-color': theme || '#6466f1',
      '--p-inputtext-border-color': theme || '#6466f1',
      '--p-textarea-border-color': theme || '#6466f1',
      '--p-radiobutton-border-color': theme || '#6466f1',
      '--p-select-border-color': theme || '#6466f1',
      '--p-inputgroup-addon-border-color': theme || '#6466f1',
      '--p-button-primary-background': primaryColors['500'],
      '--p-button-primary-border-color': primaryColors['500'],
      '--p-button-primary-hover-background': primaryColors['400'],
      '--p-button-primary-hover-border-color': primaryColors['400'],
      '--p-button-primary-active-background': primaryColors['600'],
      '--p-button-primary-active-border-color': primaryColors['600'],
      '--p-radiobutton-checked-border-color': primaryColors['600'],
      '--p-radiobutton-checked-background': primaryColors['600'],
    }

    updatePrimaryPalette(primaryColors)
  }

  function resetForm() {
    const customer_id = customerId.value
    Object.assign(customerInfo, {
      customer_id,
      customer_name: '',
      customer_email: '',
      customer_phone: '',
      customer_plan: {},
      customer_bankings: [],
      theme: '',
    })

    Object.assign(bankAccountDetails, {
      companyName: '',
      firstName: '',
      lastName: '',
      addressLine1: '',
      addressLine2: '',
      city: '',
      state: '',
      postcode: '',
      email: '',
      bsbNumber: '',
      accountNumber: '',
      bankAccountName: '',
    })

    Object.assign(paymentDetails, {
      cardNumber: '',
      expiryDate: '',
      securityCode: '',
      nameOnCard: '',
    })

    Object.assign(billingAddress, {
      country: '',
      first_name: '',
      last_name: '',
      company: '',
      address_line1: '',
      address_line2: '',
      city: '',
      state: '',
      postal_code: '',
      phone: '',
    })

    pageStatus.value = 'form'
    pageErrorMessage.value = ''
  }

  async function submitInvite(): Promise<{ success?: boolean, message?: string, html: string }> {
    try {
      setPageStatus('loading')
      const submitData: Api.CreateCustomerInviteReq = {
        customer_id: customerId.value,
        customer_name: customerInfo.customer_name,
        customer_email: customerInfo.customer_email,
        phone_number: customerInfo.customer_phone,
        type: activePaymentMethod.value === 'bankAccount' ? 2 : 1,
        currency: selectedCurrency.value,
        return_url: customerInfo.return_url,
      }

      if (activePaymentMethod.value === 'bankAccount') {
        submitData.card = {
          card_number: paymentDetails.cardNumber,
          security_code: paymentDetails.securityCode,
          name_on_card: paymentDetails.nameOnCard,
          expiration_year: paymentDetails.expiryDate.split('/')[1],
          expiration_month: paymentDetails.expiryDate.split('/')[0],
          email: paymentDetails.email,
          company_name: billingAddress.company,
          first_name: billingAddress.first_name,
          last_name: billingAddress.last_name,
          line_1: billingAddress.address_line1,
          line_2: billingAddress.address_line2,
          city: billingAddress.city,
          state: billingAddress.state,
          postcode: billingAddress.postal_code,
          country_iso2: billingAddress.country,
          phone: billingAddress.phone,
        }
      }
      else {
        submitData.bank = {
          bsb: bankAccountDetails.bsbNumber.replace(/-/g, ''),
          account_no: bankAccountDetails.accountNumber,
          account_name: bankAccountDetails.bankAccountName,
          city: bankAccountDetails.city,
          state: bankAccountDetails.state,
          postcode: bankAccountDetails.postcode,
          email: bankAccountDetails.email,
          first_name: bankAccountDetails.firstName,
          last_name: bankAccountDetails.lastName,
          line_1: bankAccountDetails.addressLine1,
          line_2: bankAccountDetails.addressLine2,
          company_name: bankAccountDetails.companyName,
        }
      }

      const response = await customerApi.submitInvite(submitData)
      if (response.code === 0) {
        if (!response.data?.html) {
          resetForm()
          setPageStatus('success')
          setPageErrorMessage('Submission failed, please try again')
          return { success: true, message: '', html: '' }
        }
        return { success: true, message: '', html: response.data.html }
      }
      else {
        setPageStatus('form')
        return { success: false, message: response.message || 'Submission failed, please try again', html: '' }
      }
    }
    catch (error) {
      setPageStatus('error')
      setPageErrorMessage(error instanceof Error ? error.message : 'Submission failed, please try again')
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Submission failed, please try again',
        html: '',
      }
    }
  }

  function setSelectedBank(bankId: number) {
    selectedBank.value = bankId
  }

  return {
    // States
    config,
    activePaymentMethod,
    selectedCurrency,
    customerInfo,
    bankAccountDetails,
    paymentDetails,
    billingAddress,
    pageStatus,
    pageErrorMessage,
    themeStyles,
    selectedBank,
    isConfirmPaymentMethod,
    isSelectedBankLoading,
    redirectCountdown,
    cssVariables,

    // Actions
    updateConfig,
    setCustomerInfo,
    setBankAccountDetails,
    setPaymentDetails,
    setBillingAddress,
    setPageStatus,
    setPageErrorMessage,
    setActivePaymentMethod,
    setSelectedCurrency,
    setCustomerId,
    resetForm,
    submitInvite,
    confirmPaymentMethod,
    cancelPaymentMethod,
    setSelectedBank,
  }
}, {
  persist: {
    omit: ['pageStatus', 'customerInfo', 'pageErrorMessage', 'selectedBank', 'isConfirmPaymentMethod', 'isSelectedBankLoading'],
  },
})
