import type { AxiosRequestConfig } from 'axios'
import { GET, POST } from '@/services/http'

export const getList = (params: Api.CustomerListReq) => GET<CommonListRes<Customer.Info[]>>('/customer/list', { params })
export const create = (data: Api.CustomerCreateReq, options?: AxiosRequestConfig) => POST<{ customer_id: string }>('/customer/create', data, options)
export const createCustomerSimple = (data: Api.CustomerCreateSimpleReq, options?: AxiosRequestConfig) => POST<{ customer_id: string }>('xero/createCustomer', data, options)
export const sendEmailInvite = (data: Api.CustomerSendEmailInviteReq) => POST<CommonRes>('/customer/sendInviteCustomerMail', data)
export const remove = (customer_ids: string[]) => POST<CommonRes>('/customer/delete', { customer_ids })
export const getCustomerDetail = (customer_id: string) => GET<Customer.Info>('/customer/detail', { params: { customer_id } })

export const getInviteConfig = (params: { customer_id: string }) => {
  let id = null

  if (params.customer_id && params.customer_id.includes('%')) {
    id = params.customer_id
  }
  else {
    id = encodeURIComponent(params.customer_id)
  }

  return GET<Api.GetCustomerInviteRes>(`/invitation/info?customer_id=${id}`, {
    paramsSerializer: {
      encode: v => v,
    },
  })
}

export const submitInvite = (data: Api.CreateCustomerInviteReq | Api.CreateCustomerInviteChangePaymentMethodReq) => POST<{ html: string }>('/invitation/submit', data)

export const updateCustomerPlan = (data: Api.UpdateCustomerPlanReq) => POST<CommonRes>('/plan/assignToCustomer', data)

export const createPayment = (data: Api.CreateCustomerPaymentReq) => POST<CommonRes>('/trans/create', data)

export const updateCustomer = (data: Api.UpdateCustomerReq) => POST<CommonRes>('/customer/update', data)

export const cancelSubscription = (customer_id: string, plan_id: string) => POST<CommonRes>('/plan/cancelFromCustomer', { customer_id, plan_id })

export const exportCustomers = (params: Api.CustomerListReq) => GET('/customer/export', { params })

export const getCustomerPlanDetail = (plan_id: string, customer_id: string) => GET<Plan.Info>('/subscription/detail', { params: { plan_id, customer_id } })

export const cancelCustomerPlanSubscription = (plan_id: string, customer_id: string) => POST<CommonRes>('/subscription/cancel', { plan_id, customer_id })

export const updateCustomerPlanSubscription = (data: Api.CustomerUpdateSubscriptionReq) => POST<CommonRes>('/subscription/update', data)

export const getCustomerHistory = (params: Api.CustomerHistoryListReq) => GET<CommonListRes<Customer.History>>('/customer/communicationHistory', { params })

export const getCustomerConfig = () => GET<Api.GetCustomerConfigRes>('/getMerchantConfig')

export const updateCommunicationConfig = (data: Api.UpdateCommunicationConfigReq) => POST<CommonRes>('/customer/notificationSwitch', data)

// 获取客户列表
export const getAllCustomer = (params: Api.GetAllCustomerReq, options?: AxiosRequestConfig) => GET<Customer.Info[]>('xero/getAllCustomer', { params, ...options })

export const updateCommunicationTheme = (data: Api.UpdateCommunicationThemeReq) => POST<CommonRes>('/communicationConfig/updateLogoTheme', data)
