declare namespace Invoice {
  interface Info {
    business_id?: string
    /**
     * 主题ID
     */
    branding_theme_id: string
    /**
     * 邮件预览内容
     */
    email_html: string
    /**
     * 是否添加子项，0-否 1-是
     */
    is_line_items: number
    reference: string
    /**
     * 创建时间
     */
    created_at: string
    /**
     * 币种
     */
    currency: string
    /**
     * 客户数据
     */
    customer: Customer.Info
    /**
     * 客户ID
     */
    customer_id: string
    /**
     * 截止时间
     */
    due_date: string
    id: number
    /**
     * 渠道发票编号
     */
    invoice_number: string
    /**
     * MID
     */
    merchant_id: string
    /**
     * 状态
     */
    status: number
    /**
     * 发票总金额
     */
    sub_total: string
    total_tax: string
    is_inclusive_gst: 0 | 1
    /**
     * 交易数据
     */
    timeline?: Timeline[]
    payment_method?: PaymentMethod

    line_items?: LineItem[]
    payment_url?: string

    invoice_pdf_url?: string

    gst_rate?: string

    total_gst?: string

    total_surcharge?: string

    merchant?: {
      merchant_name: string
      merchant_id: string
    }

    transaction: {
      payment_amount: string
      payment_currency: string
      status: number
      trans_invoice_id: number
      trans_no: string
    }[]
  }

  interface Timeline {
    payment_amount: string
    payment_currency: string
    status: number
    trans_invoice_id: number
    trans_no: string
  }

  interface PaymentMethod {
    /**
     * 卡号
     */
    account_no: string
    /**
     * 交易时间
     */
    created_at: string
    /**
     * 卡品牌
     */
    credit_brand: number
    /**
     * 订单号
     */
    trans_no: string
  }
  interface LineItem {
    account_code: string
    invoice_id: number
    description: string
    id: number
    line_item_id: string
    line_amount: string
    quantity: string
    unit_amount: string
    xero_invoice_id: number
  }

  interface DashboardData {
    count: number
    /**
     * 状态常量
     */
    status: number | null
    /**
     * 状态名称
     */
    status_text: string
  }

}
