<script setup lang="ts">
import { Utils } from '@shared'
import { onMounted, ref } from 'vue'
import { getTemplate } from '@/services/api/notification'
import { THEME_COLOR_MAP, THEME_COLORS } from '../theme'
import customerUpload from './customerUpload.vue'

const props = defineProps({
  mode: {
    type: String,
    default: 'template',
  },
  template: {
    type: Array,
    default: () => [],
  },
  isShowTip: {
    type: Boolean,
    default: true,
    required: false,
  },
  submitLoading: {
    type: Boolean,
    default: false,
  },
})

const emits = defineEmits(['update:logo', 'update:selectedColor', 'update:changeContent', 'update:changeTemplateType', 'updateTheme', 'cancelTheme'])

const logoModel = defineModel<FileUpload.UploadFileItem[]>('logo')

// 预设颜色面板 - 使用主题key映射
const presetColors = ref([
  { themeKey: THEME_COLORS.LIGHT_ORANGE, ...THEME_COLOR_MAP[THEME_COLORS.LIGHT_ORANGE] },
  { themeKey: THEME_COLORS.LIGHT_YELLOW, ...THEME_COLOR_MAP[THEME_COLORS.LIGHT_YELLOW] },
  { themeKey: THEME_COLORS.BLUE, ...THEME_COLOR_MAP[THEME_COLORS.BLUE] },
  { themeKey: THEME_COLORS.LIGHT_RED, ...THEME_COLOR_MAP[THEME_COLORS.LIGHT_RED] },
  { themeKey: THEME_COLORS.PINK, ...THEME_COLOR_MAP[THEME_COLORS.PINK] },
  { themeKey: THEME_COLORS.PURPLE, ...THEME_COLOR_MAP[THEME_COLORS.PURPLE] },
  { themeKey: THEME_COLORS.LIGHT_PINK, ...THEME_COLOR_MAP[THEME_COLORS.LIGHT_PINK] },
  { themeKey: THEME_COLORS.CYAN, ...THEME_COLOR_MAP[THEME_COLORS.CYAN] },
  { themeKey: THEME_COLORS.DARK_GRAY, ...THEME_COLOR_MAP[THEME_COLORS.DARK_GRAY] },
  { themeKey: THEME_COLORS.ORANGE, ...THEME_COLOR_MAP[THEME_COLORS.ORANGE] },
])

// 当前选中的颜色组合
const selectedColors = ref({
  themeKey: THEME_COLORS.LIGHT_ORANGE as keyof typeof THEME_COLOR_MAP,
  backgroundColor: '#FCE38A',
  color: '#8B4513',
})

// 最近使用的颜色组合
const recentColors = ref([
  { name: 'orange', backgroundColor: '#fe4c1c', color: '#ffffff' },
  { name: 'blue', backgroundColor: '#09deff', color: '#ffffff' },
  { name: 'green', backgroundColor: '#e1ffa9', color: '#000000' },
  { name: 'pink', backgroundColor: '#ffe3e8', color: '#000000' },
  { name: 'dark', backgroundColor: '#181349', color: '#ffffff' },
  { name: 'light', backgroundColor: '#ffffff', color: '#000000' },
])

const colorValue = ref('#ef5129')

const templateValue = ref()

// 选择最近使用的颜色组合
const selectRecentColor = (colorConfig: { name: string, backgroundColor: string, color: string }) => {
  selectedColors.value = {
    themeKey: colorConfig.name as keyof typeof THEME_COLOR_MAP || THEME_COLORS.CUSTOM,
    backgroundColor: colorConfig.backgroundColor,
    color: colorConfig.color,
  }

  // 更新当前颜色值为背景色（用于显示）
  colorValue.value = colorConfig.backgroundColor

  // 将颜色组合添加到最近使用的颜色列表中
  addToRecentColors(colorConfig.backgroundColor, colorConfig.color)

  emits('update:selectedColor', colorConfig.backgroundColor)
}

const changeColor = Utils.throttle((color: any) => {
  const selectedColor = color.slice(0, 1) === '#' ? color : `#${color}`

  // 将选中的颜色添加到最近使用的颜色列表中（默认白色文字）
  addToRecentColors(selectedColor, '#ffffff')

  emits('update:selectedColor', selectedColor)
}, 300, {
  trailing: true,
})

// 选择预设颜色组合（背景色 + 字体色）
const selectPresetColors = (themeKey: keyof typeof THEME_COLOR_MAP) => {
  const colorConfig = THEME_COLOR_MAP[themeKey]

  selectedColors.value = {
    themeKey,
    backgroundColor: colorConfig.backgroundColor,
    color: colorConfig.color,
  }

  // 更新当前颜色值为背景色（用于显示）
  colorValue.value = colorConfig.backgroundColor

  // 将颜色组合添加到最近使用的颜色列表中
  addToRecentColors(colorConfig.backgroundColor, colorConfig.color)

  emits('update:selectedColor', themeKey)
}

// 添加颜色组合到最近使用列表
const addToRecentColors = (backgroundColor: string, textColor: string = '#ffffff') => {
  // 检查颜色组合是否已经存在
  const existingIndex = recentColors.value.findIndex(item =>
    item.backgroundColor.toLowerCase() === backgroundColor.toLowerCase()
    && item.color.toLowerCase() === textColor.toLowerCase(),
  )

  if (existingIndex !== -1) {
    // 如果存在，将其移到最前面
    const existingColor = recentColors.value.splice(existingIndex, 1)[0]
    recentColors.value.unshift(existingColor)
  }
  else {
    // 如果不存在，添加到最前面
    recentColors.value.unshift({
      name: `Custom ${backgroundColor}`,
      backgroundColor,
      color: textColor,
    })

    // 保持最多6个最近使用的颜色
    if (recentColors.value.length > 6) {
      recentColors.value = recentColors.value.slice(0, 6)
    }
  }
}

const messageTemplate = ref()
const handleAccordionOpen = async (event: { index: number }) => {
  emits('update:changeContent', findTemplate(event.index))
}
const findTemplate = (index: number) => {
  if (templateValue.value) {
    return templateValue.value.find((item: Api.NotificationListTemplate) => item.notification_type === index).content
  }
  return ''
}

const cancelMessage = () => {
  emits('update:changeContent', '')
}

const saveTemplate = (type: number) => {
  emits('update:changeTemplateType', type)
}

onMounted(() => {
  Promise.all([
    getTemplate().then((res) => {
      templateValue.value = res.data.data
    }),
  ])
})

defineExpose({
  selectedColors,
})
</script>

<template>
  <div class="edit">
    <div class="multiple">
      <Accordion multiple :value="['0']" expand-icon="pi pi-sort-down-fill" collapse-icon="pi pi-sort-up-fill">
        <AccordionPanel value="0">
          <AccordionHeader>
            <span class="flex items-center gap-2 w-full">
              <span class="font-bold text-xl whitespace-nowrap">Logo and Colors</span>
            </span>
          </AccordionHeader>
          <AccordionContent>
            <div class="flex gap-6 max-w-full">
              <div class="flex-1 flex flex-col gap-4">
                <label class="block text-900 font-medium">Add Your Logo</label>
                <customerUpload
                  v-model:model-value="logoModel" mode="logo" :multiple="false" :max-files="1"
                  accept="image/*"
                />
              </div>
              <div class="flex-1 flex flex-col gap-4">
                <label class="block ">Pick Your Colors</label>
                <div class="color-picker-section">
                  <!-- 预设颜色面板 -->
                  <div class="preset-colors-section mb-4">
                    <div class="preset-colors-grid grid grid-cols-5 gap-3">
                      <div
                        v-for="(color, index) in presetColors"
                        :key="index"
                        class="preset-color-item-container"
                        :class="{ selected: selectedColors.themeKey === color.themeKey }"
                        :title="`Background: ${color.backgroundColor}, Text: ${color.color}`"
                        @click="selectPresetColors(color.themeKey)"
                      >
                        <!-- 左边颜色 (背景色) -->
                        <div
                          class="preset-color-half left-color"
                          :style="{ backgroundColor: color.backgroundColor }"
                          :title="`Background: ${color.backgroundColor}`"
                        />
                        <!-- 右边颜色 (字体色) -->
                        <div
                          class="preset-color-half right-color"
                          :style="{ backgroundColor: color.color }"
                          :title="`Text: ${color.color}`"
                        />
                      </div>
                    </div>
                  </div>

                  <!-- 最近使用和自定义颜色选择器 -->
                  <div class="flex justify-between items-end">
                    <div>
                      <span class="text-sm font-medium text-gray-700">Recently Used</span>
                      <div class="recent-colors flex gap-2 mt-2">
                        <div
                          v-for="(color, index) in recentColors"
                          :key="index"
                          class="recent-color-item"
                          :style="{ backgroundColor: color.backgroundColor }"
                          :title="`${color.name} - Background: ${color.backgroundColor}, Text: ${color.color}`"
                          @click="selectRecentColor(color)"
                        />
                      </div>
                    </div>
                    <div class="flex flex-col items-end">
                      <span class="text-sm font-medium text-gray-700 mb-2">Custom Color</span>
                      <ColorPicker
                        v-model="colorValue"
                        @value-change="changeColor"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div v-if="props.mode === 'template'" class="button-group flex justify-end gap-2 mt-4">
              <Button
                :loading="props.submitLoading" label="CANCEL" severity="secondary" style="--p-button-label-font-weight: 600" class="!px-8"
                @click="emits('cancelTheme')"
              />
              <Button
                :loading="props.submitLoading" severity="warn" label="SAVE CHANGE" style="--p-button-label-font-weight: 600" class="!px-8"
                @click="emits('updateTheme')"
              />
            </div>
          </AccordionContent>
        </AccordionPanel>
        <AccordionPanel value="1">
          <AccordionHeader>
            <span class="flex items-center gap-2 w-full">
              <span class="font-bold text-xl whitespace-nowrap">URL</span>
            </span>
          </AccordionHeader>
          <AccordionContent>
            <div class="flex justify-between gap-10">
              <div class="address w-1/2">
                <label class="block text-900 font-medium mb-2">URL Address</label>
                <InputText disabled placeholder="https://" class="w-full" />
              </div>
              <div class="redirection w-1/2">
                <label class="block text-900 font-medium mb-2">URD redirection after success</label>
                <InputText disabled class="w-full" />
              </div>
            </div>
          </AccordionContent>
        </AccordionPanel>
      </Accordion>
    </div>
    <div class="radio mt-6">
      <Accordion
        v-if="props.mode === 'template'" :value="messageTemplate" expand-icon="pi pi-sort-down-fill"
        collapse-icon="pi pi-sort-up-fill" @tab-open="handleAccordionOpen"
      >
        <AccordionPanel :value="6">
          <AccordionHeader>
            <span class="flex items-center gap-2 w-full">
              <span class="font-bold text-xl whitespace-nowrap">Invite a customer</span>
            </span>
          </AccordionHeader>
          <AccordionContent>
            <div class="p-2">
              <div class="flex w-1/2 items-center mb-2">
                <h3 class=" font-semibold ">
                  Message
                </h3>
                <Tag value="Optional" class="  ml-4" />
              </div>
              <!-- <Textarea v-model="value" rows="5" cols="96" disabled style="resize: none" /> -->
              <Card>
                <template #content>
                  <p v-html="findTemplate(6)" />
                </template>
              </Card>
              <div class="button-group flex justify-end gap-2 mt-4">
                <Button
                  :loading="props.submitLoading" label="CANCEL" severity="secondary" style="--p-button-label-font-weight: 600" class="!px-8"
                  @click="cancelMessage"
                />
                <Button
                  :loading="props.submitLoading" severity="warn" label="SAVE CHANGE" style="--p-button-label-font-weight: 600" class="!px-8"
                  @click="saveTemplate(6)"
                />
              </div>
            </div>
          </AccordionContent>
        </AccordionPanel>
        <AccordionPanel :value="4">
          <AccordionHeader>
            <span class="flex  items-center gap-2 w-full">
              <span class="font-bold text-xl whitespace-nowrap">Confirmation of subscription</span>
            </span>
          </AccordionHeader>
          <AccordionContent>
            <div class="p-2">
              <div class="flex w-1/2 items-center mb-2">
                <h3 class=" font-semibold ">
                  Message
                </h3>
                <Tag value="Optional" class="  ml-4" />
              </div>
              <!-- <Textarea v-model="value" rows="5" cols="96" disabled style="resize: none" /> -->
              <Card>
                <template #content>
                  <p v-html="findTemplate(4)" />
                </template>
              </Card>
              <div class="button-group flex justify-end gap-2 mt-4">
                <Button
                  :loading="props.submitLoading" label="CANCEL" severity="secondary" style="--p-button-label-font-weight: 600" class="!px-8"
                  @click="cancelMessage"
                />
                <Button
                  :loading="props.submitLoading" severity="warn" label="SAVE CHANGE" style="--p-button-label-font-weight: 600" class="!px-8"
                  @click="saveTemplate(4)"
                />
              </div>
            </div>
          </AccordionContent>
        </AccordionPanel>
        <AccordionPanel :value="1">
          <AccordionHeader>
            <span class="flex  items-center gap-2 w-full">
              <span class="font-bold text-xl whitespace-nowrap">Successful payment</span>
            </span>
          </AccordionHeader>
          <AccordionContent>
            <div class="p-2">
              <div class="flex w-1/2 items-center mb-2">
                <h3 class=" font-semibold ">
                  Message
                </h3>
                <Tag value="Optional" class="  ml-4" />
              </div>
              <!-- <Textarea v-model="value" rows="5" cols="96" disabled style="resize: none" /> -->
              <Card>
                <template #content>
                  <p v-html="findTemplate(1)" />
                </template>
              </Card>
              <div class="button-group flex justify-end gap-2 mt-4">
                <Button
                  :loading="props.submitLoading" label="CANCEL" severity="secondary" style="--p-button-label-font-weight: 600" class="!px-8"
                  @click="cancelMessage"
                />
                <Button
                  :loading="props.submitLoading" severity="warn" label="SAVE CHANGE" style="--p-button-label-font-weight: 600" class="!px-8"
                  @click="saveTemplate(1)"
                />
              </div>
            </div>
          </AccordionContent>
        </AccordionPanel>
        <AccordionPanel :value="2">
          <AccordionHeader>
            <span class="flex  items-center gap-2 w-full">
              <span class="font-bold text-xl whitespace-nowrap">Upcoming payment</span>
            </span>
          </AccordionHeader>
          <AccordionContent>
            <div class="p-2">
              <div class="flex w-1/2 items-center mb-2">
                <h3 class=" font-semibold ">
                  Message
                </h3>
                <Tag value="Optional" class="  ml-4" />
              </div>
              <!-- <Textarea v-model="value" rows="5" cols="96" disabled style="resize: none" /> -->
              <Card>
                <template #content>
                  <p v-html="findTemplate(2)" />
                </template>
              </Card>
              <div class="button-group flex justify-end gap-2 mt-4">
                <Button
                  :loading="props.submitLoading" label="CANCEL" severity="secondary" style="--p-button-label-font-weight: 600" class="!px-8"
                  @click="cancelMessage"
                />
                <Button
                  :loading="props.submitLoading" severity="warn" label="SAVE CHANGE" style="--p-button-label-font-weight: 600" class="!px-8"
                  @click="saveTemplate(2)"
                />
              </div>
            </div>
          </AccordionContent>
        </AccordionPanel>
        <AccordionPanel :value="8">
          <AccordionHeader>
            <span class="flex  items-center gap-2 w-full">
              <span class="font-bold text-xl whitespace-nowrap">Subscription update</span>
            </span>
          </AccordionHeader>
          <AccordionContent>
            <div class="p-2">
              <div class="flex w-1/2 items-center mb-2">
                <h3 class=" font-semibold ">
                  Message
                </h3>
                <Tag value="Optional" class="  ml-4" />
              </div>
              <!-- <Textarea v-model="value" rows="5" cols="96" disabled style="resize: none" /> -->
              <Card>
                <template #content>
                  <p v-html="findTemplate(8)" />
                </template>
              </Card>
              <div class="button-group flex justify-end gap-2 mt-4">
                <Button
                  :loading="props.submitLoading" label="CANCEL" severity="secondary" style="--p-button-label-font-weight: 600" class="!px-8"
                  @click="cancelMessage"
                />
                <Button
                  :loading="props.submitLoading" severity="warn" label="SAVE CHANGE" style="--p-button-label-font-weight: 600" class="!px-8"
                  @click="saveTemplate(8)"
                />
              </div>
            </div>
          </AccordionContent>
        </AccordionPanel>
        <AccordionPanel :value="3">
          <AccordionHeader>
            <span class="flex  items-center gap-2 w-full">
              <span class="font-bold text-xl whitespace-nowrap">Fail/Retry payment</span>
            </span>
          </AccordionHeader>
          <AccordionContent>
            <div class="p-2">
              <div class="flex w-1/2 items-center mb-2">
                <h3 class=" font-semibold ">
                  Message
                </h3>
                <Tag value="Optional" class="  ml-4" />
              </div>
              <!-- <Textarea v-model="value" rows="5" cols="96" disabled style="resize: none" /> -->
              <Card>
                <template #content>
                  <p v-html="findTemplate(3)" />
                </template>
              </Card>
              <div class="button-group flex justify-end gap-2 mt-4">
                <Button
                  :loading="props.submitLoading" label="CANCEL" severity="secondary" style="--p-button-label-font-weight: 600" class="!px-8"
                  @click="cancelMessage"
                />
                <Button
                  :loading="props.submitLoading" severity="warn" label="SAVE CHANGE" style="--p-button-label-font-weight: 600" class="!px-8"
                  @click="saveTemplate(3)"
                />
              </div>
            </div>
          </AccordionContent>
        </AccordionPanel>
        <AccordionPanel :value="9">
          <AccordionHeader>
            <span class="flex  items-center gap-2 w-full">
              <span class="font-bold text-xl whitespace-nowrap">Subscription cancel</span>
            </span>
          </AccordionHeader>
          <AccordionContent>
            <div class="p-2">
              <div class="flex w-1/2 items-center mb-2">
                <h3 class=" font-semibold ">
                  Message
                </h3>
                <Tag value="Optional" class="  ml-4" />
              </div>
              <!-- <Textarea v-model="value" rows="5" cols="96" disabled style="resize: none" /> -->
              <Card>
                <template #content>
                  <p v-html="findTemplate(9)" />
                </template>
              </Card>
              <div class="button-group flex justify-end gap-2 mt-4">
                <Button
                  :loading="props.submitLoading" label="CANCEL" severity="secondary" style="--p-button-label-font-weight: 600" class="!px-8"
                  @click="cancelMessage"
                />
                <Button
                  :loading="props.submitLoading" severity="warn" label="SAVE CHANGE" style="--p-button-label-font-weight: 600" class="!px-8"
                  @click="saveTemplate(9)"
                />
              </div>
            </div>
          </AccordionContent>
        </AccordionPanel>
        <AccordionPanel :value="5">
          <AccordionHeader>
            <span class="flex  items-center gap-2 w-full">
              <span class="font-bold text-xl whitespace-nowrap">Payment method update</span>
            </span>
          </AccordionHeader>
          <AccordionContent>
            <div class="p-2">
              <div class="flex w-1/2 items-center mb-2">
                <h3 class=" font-semibold ">
                  Message
                </h3>
                <Tag value="Optional" class="  ml-4" />
              </div>
              <!-- <Textarea v-model="value" rows="5" cols="96" disabled style="resize: none" /> -->

              <Card>
                <template #content>
                  <p v-html="findTemplate(5)" />
                </template>
              </Card>
              <div class="button-group flex justify-end gap-2 mt-4">
                <Button
                  :loading="props.submitLoading" label="CANCEL" severity="secondary" style="--p-button-label-font-weight: 600" class="!px-8"
                  @click="cancelMessage"
                />
                <Button
                  :loading="props.submitLoading" severity="warn" label="SAVE CHANGE" style="--p-button-label-font-weight: 600" class="!px-8"
                  @click="saveTemplate(5)"
                />
              </div>
            </div>
          </AccordionContent>
        </AccordionPanel>
      </Accordion>
      <Accordion v-else :value="0" expand-icon="pi pi-sort-down-fill" collapse-icon="pi pi-sort-up-fill">
        <AccordionPanel :value="0">
          <AccordionHeader>
            <span class="flex items-center gap-2 w-full">
              <span class="font-bold text-xl whitespace-nowrap">Message</span>
            </span>
          </AccordionHeader>
          <AccordionContent>
            <div class="p-2">
              <div class="flex w-1/2 items-center mb-2">
                <h3 class=" font-semibold ">
                  Custom Message
                </h3>
                <Tag value="Optional" class="  ml-4" />
              </div>
              <Card>
                <template #content>
                  <p v-html="findTemplate(6)" />
                </template>
              </Card>
              <div v-if="props.isShowTip" class="text-[#b1b1b1]  ml-4 mt-2">
                If you don't add a custom message, we'll default to GoCardless' messaging.
              </div>
              <!-- <div class="button-group flex justify-end gap-2 mt-4">
                <button class="cancel-btn">
                  CANCEL
                </button>
                <button class="save-btn">
                  SAVE CHANGE
                </button>
              </div> -->
            </div>
          </AccordionContent>
        </AccordionPanel>
      </Accordion>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.edit {
  --p-accordion-header-toggle-icon-color: #fe4c1c;
  --p-accordion-header-toggle-icon-active-color: #fe4c1c;
}

.cancel-btn,
.save-btn {
  padding: 10px 30px;
  background-color: var(--color-white-100);
  border: none;
  color: #181349;
  font-weight: 700;
  border-radius: 7px;
  font-size: 12px;
  cursor: pointer;
}

.cancel-btn:hover {
  background-color: #dcdce4;
}

.save-btn {
  background-color: #fe4c1c;
  color: #fff;
}

.save-btn:hover {
  background-color: #e94618;
}

.color-picker-section {
  width: 100%;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 1rem;
  background-color: #fafafa;
}

.current-color-display {
  .current-color-preview {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
}

.preset-colors-section {
  .preset-colors-grid {
    gap: 8px;
  }
}

.preset-color-item-container {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  overflow: hidden;
  border: 2px solid #e5e7eb;
  display: flex;
  transition: all 0.2s ease;
  cursor: pointer;

  &:hover {
    transform: scale(1.05);
    border-color: #9ca3af;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }

  &.selected {
    border-color: #fe4c1c;
    border-width: 3px;
    box-shadow: 0 0 0 2px rgba(254, 76, 28, 0.2);
  }

  &.selected:hover {
    border-color: #e94618;
  }
}

.preset-color-half {
  width: 50%;
  height: 100%;
  position: relative;

  &.left-color {
    border-right: 1px solid rgba(255, 255, 255, 0.3);
  }

  &.right-color {
    border-left: 1px solid rgba(255, 255, 255, 0.3);
  }
}

.recent-color-item {
  border: 2px solid #e5e7eb;
  border-radius: 6px;
  width: 24px;
  height: 24px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    transform: scale(1.1);
    border-color: #9ca3af;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  &:active {
    transform: scale(0.9);
  }
}
</style>
