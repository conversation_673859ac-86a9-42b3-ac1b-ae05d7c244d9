<script setup lang="ts">
import Decimal from 'decimal.js'
import QRCode from 'qrcode'
import { computed, onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { InvoiceStatus } from '@/constants/invoice'
import { invoice as invoiceApi } from '@/services/api'
import { copyToClipboard } from '@/utils/clipboard'
import { formatDate } from '@/utils/date'

const route = useRoute()

const router = useRouter()
// State
const invoiceInfo = ref<Invoice.Info>({} as Invoice.Info)
const loading = ref(true)

const sendLoading = ref(false)

// Computed property to wrap email HTML with basic styles
const emailHtmlWithStyles = computed(() => {
  if (!invoiceInfo.value?.email_html) { return '' }

  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1">
      <style>
        body {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          line-height: 1.6;
          color: #333;
          max-width: 100%;
          margin: 0;
          padding: 16px;
          background: white;
        }
        img {
          max-width: 100%;
          height: auto;
        }
        table {
          border-collapse: collapse;
          width: 100%;
        }
        th, td {
          border: 1px solid #ddd;
          padding: 8px;
          text-align: left;
        }
        th {
          background-color: #f2f2f2;
          font-weight: bold;
        }
      </style>
    </head>
    <body>
      ${invoiceInfo.value.email_html}
    </body>
    </html>
  `
})

// Fetch invoice data
const fetchInvoice = async () => {
  try {
    loading.value = true
    const data = await invoiceApi.getInvoiceDetail({ id: Number(route.params.id) })
    invoiceInfo.value = data.data
  }
  catch {
    window.$toast.add({ severity: 'error', summary: 'Error', detail: 'Failed to load invoice details' })
    loading.value = false
  }
  finally {
    loading.value = false
  }
}

const sendInvoiceEmail = () => {
  window.$confirm.require({
    message: 'Are you sure you want to send this invoice?',
    header: 'Send Invoice',
    icon: 'pi pi-envelope',
    acceptClass: 'p-button-warn',
    rejectClass: 'p-button-primary',
    accept: () => {
      sendLoading.value = true
      invoiceApi.sendInvoiceEmail({ id: Number(route.params.id) }).then((res) => {
        if (res.code === 0) {
          window.$toast.add({ severity: 'success', summary: 'Success', detail: 'Invoice sent successfully' })
        }
      }).finally(() => {
        sendLoading.value = false
      })
    },
    reject: () => {

    },
  })
}
const downLoadQrCodeLoading = ref(false)
const canvas = ref<HTMLCanvasElement | null>(null)

const copyLink = () => {
  copyToClipboard(invoiceInfo.value?.payment_url || '', () => window.$toast.add({ severity: 'success', summary: 'Success', detail: 'Copy successfully' }))
}

const downloadQrCode = () => {
  downLoadQrCodeLoading.value = true
  const options = {
    size: 300,
    dark: '#000000',
    light: '#ffffff',
  }
  const url = invoiceInfo.value?.payment_url || ''
  if (!url) {
    return window.$toast.add({ severity: 'error', summary: 'Error', detail: 'Please refresh and try again.' })
  }
  try {
    QRCode.toCanvas(
      canvas.value,
      url,
      {
        width: options.size,
        margin: 2,
        color: {
          dark: options.dark,
          light: options.light,
        },
      },
      (error) => {
        if (error) {
          console.error(error)
        }
      },
    )
  }
  catch (e) {
    console.error(e)
  }
  try {
    const link = document.createElement('a')
    link.download = `Payment_QRCode_${new Date().getTime()}.png`
    link.href = canvas?.value?.toDataURL('image/png') || ''
    link.click()
  }
  catch (e) {
    console.error(e)
  }
  downLoadQrCodeLoading.value = false
}

const downloadInvoice = () => {
  const url = invoiceInfo.value?.invoice_pdf_url || ''
  if (!url) {
    return window.$toast.add({ severity: 'error', summary: 'Error', detail: 'Please refresh and try again.' })
  }
  window.open(url, '_blank')
}

const backToEdit = () => {
  router.push({
    name: 'payMyInvoiceEditInvoice',
    params: { id: route.params.id },
  })
}

// Lifecycle hooks
onMounted(() => {
  fetchInvoice()
})
</script>

<template>
  <div class="pay-invoice-wrap flex flex-col lg:flex-row gap-4">
    <div class="pay-invoice-container lg:w-3/5">
      <!-- Header with actions -->
      <div class="header-actions">
        <div class="action-buttons">
          <div>
            <Button v-if="invoiceInfo?.status === InvoiceStatus.UNPAID" icon="pi pi-chevron-left" severity="info" label="Back to Edit" @click="backToEdit" />
          </div>
          <div class="flex gap-2">
            <Button v-if="invoiceInfo?.payment_url && [InvoiceStatus.UNPAID, InvoiceStatus.PARTIAL].includes(invoiceInfo?.status)" icon="pi pi-copy" label="Copy Link" @click="copyLink" />
            <Button
              v-if="invoiceInfo?.payment_url && [InvoiceStatus.UNPAID, InvoiceStatus.PARTIAL].includes(invoiceInfo?.status)" icon="pi pi-download" severity="help" label="Download QR Code"
              :loading="downLoadQrCodeLoading" @click="downloadQrCode"
            />
            <Button
              v-if="invoiceInfo?.payment_url" icon="pi pi-file-pdf" severity="info" label="Download Invoice"
              :loading="downLoadQrCodeLoading" @click="downloadInvoice"
            />
            <Button
              icon="pi pi-envelope" severity="warn" label="Send Email" :loading="sendLoading"
              @click="sendInvoiceEmail"
            />
            <canvas v-show="false" ref="canvas" />
          </div>
        </div>
      </div>

      <!-- Loading state -->
      <div v-if="loading" class="loading-container">
        <ProgressSpinner />
        <p>Loading invoice details...</p>
      </div>

      <!-- Invoice content -->
      <Card v-else-if="invoiceInfo" class="invoice-card">
        <template #content>
          <!-- Invoice header -->
          <div class="invoice-header">
            <div class="invoice-parties">
              <div class="recipient">
                <h3>TAX INVOICE</h3>
                <p>{{ invoiceInfo.customer.name }}</p>
              </div>
            </div>

            <div class="invoice-right">
              <div class="invoice-meta">
                <div class="meta-item">
                  <h3>Invoice number</h3>
                  <p>{{ invoiceInfo.invoice_number }}</p>
                </div>
                <div class="meta-item">
                  <h3>Issue date</h3>
                  <p>{{ formatDate(invoiceInfo.created_at, 'DD MMM YYYY') }}</p>
                </div>
                <div class="meta-item">
                  <h3>Due date</h3>
                  <p
                    :class="{ 'due-today': invoiceInfo.due_date === 'Today' }"
                  >
                    {{ formatDate(invoiceInfo.due_date, 'DD MMM YYYY') }}
                  </p>
                </div>
                <div v-if="invoiceInfo?.reference" class="meta-item">
                  <h3>Reference</h3>
                  <p class="text-ellipsis max-w-[200px]">
                    {{ invoiceInfo?.reference }}
                  </p>
                </div>
              </div>
              <div class="merchant-name">
                <h3>{{ invoiceInfo.merchant?.merchant_name }}</h3>
              </div>
            </div>
          </div>

          <Divider />

          <!-- Invoice items -->
          <div class="invoice-items">
            <table>
              <thead>
                <tr>
                  <th>Description</th>
                  <th>Quantity</th>
                  <th>Unit Price</th>
                  <th>GST</th>
                  <th>Amount AUD</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="(item, index) in invoiceInfo.line_items" :key="index">
                  <td>{{ item.description }}</td>
                  <td>{{ Number(item.quantity).toFixed(2).toString() }}</td>
                  <td>{{ item.unit_amount }}</td>
                  <td v-if="invoiceInfo.gst_rate">
                    {{ invoiceInfo.gst_rate }} %
                  </td>
                  <td v-else>
                    GST Free
                  </td>
                  <td>{{ item.line_amount }}</td>
                </tr>
                <tr>
                  <td class="text-right" colspan="4" style="border-bottom: none;">
                    Subtotal
                  </td>
                  <td style="border-bottom: none;">
                    {{ invoiceInfo.sub_total }}
                  </td>
                </tr>
                <tr v-if="Number(invoiceInfo.total_tax) !== 0">
                  <td class="text-right" colspan="4" style="border-bottom: none;">
                    TOTAL GST
                    <template v-if="Number(invoiceInfo.total_tax) !== 0">
                      {{ invoiceInfo.gst_rate }} %
                    </template>
                  </td>
                  <td style="border-bottom: none;">
                    {{ new Decimal(invoiceInfo?.total_gst || 0).toFixed(2) }}
                  </td>
                </tr>
                <tr v-if="invoiceInfo.total_surcharge && Number(invoiceInfo.total_surcharge) > 0">
                  <td class="text-right" colspan="4" style="border-bottom: none;">
                    TOTAL Surcharge
                  </td>
                  <td style="border-bottom: none;">
                    {{ new Decimal(invoiceInfo?.total_surcharge).toFixed(2) }}
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- Invoice summary -->
          <div class="invoice-summary">
            <!-- <div class="summary-row">
              <span>TOTAL GST 10%</span>
              <span>{{ invoiceInfo.sub_total }}</span>
            </div> -->

            <div class="amount-due flex justify-between items-center">
              <h3>TOTAL {{ invoiceInfo.currency }} </h3>
              <h2>{{ new Decimal(invoiceInfo.sub_total).add(new Decimal(invoiceInfo.total_tax)).toFixed(2) }}</h2>
            </div>
          </div>
        </template>
      </Card>

      <!-- Error state -->
      <div v-else class="error-container">
        <i class="pi pi-exclamation-triangle" style="font-size: 2rem" />
        <p>Failed to load invoice. Please try again.</p>
        <Button label="Retry" @click="fetchInvoice" />
      </div>
    </div>
    <div class="preview-container lg:w-2/5 p-2 bg-white rounded-xl">
      <iframe
        :srcdoc="emailHtmlWithStyles"
        class="email-iframe"
        frameborder="0"
        scrolling="auto"
      />
    </div>
  </div>
</template>

<style scoped lang="scss">
.pay-invoice-container {
  max-width: 1000px;
  // margin: 0 auto;
  padding: 2rem 1rem;
  padding-left: 0;

}

.preview-container {
  padding: 2rem 1rem;
}

.header-actions {
  // display: flex;
  // align-items: center;
  // justify-content: flex-end;
  margin-bottom: 2rem;

  h1 {
    flex: 1;
    margin: 0 1rem;
    font-size: 1.5rem;
  }
}

.action-buttons {
  display: flex;
  // gap: 0.5rem;
  justify-content: space-between;

}

.invoice-card {
  margin-bottom: 2rem;
}

.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
  background-color: #f9f9f9;
  border-radius: 8px;
  margin: 1rem 0;
}

.invoice-header {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin-bottom: 3rem;
}

.invoice-parties,
.invoice-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 2rem;
}

.invoice-right {
  display: flex;
  justify-content: space-between;
  gap: 4rem;
}

.invoice-meta {
  flex-direction: column;
}

.meta-item,
.recipient,
.sender {
  h3 {
    font-size: 0.9rem;
    // color: #666;
    color: #000a1b;
    margin-bottom: 0.5rem;
    font-weight: 500;
  }

  p {
    font-size: 1rem;
    margin: 0;
  }
}

.meta-item {
  h3 {
    font-weight: 600;
    font-size: 1rem;
  }
}

.recipient {
  h3 {
    font-size: 3rem;
  }

  p {
    display: flex;
    justify-content: flex-end;
  }
}

.due-today {
  color: #f59e0b;
  font-weight: 600;
}

.overdue {
  color: #ef4444;
  font-weight: 600;
}

.status-badge {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.85rem;
  font-weight: 500;

  &.status-pending {
    background-color: #f3f4f6;
    color: #4b5563;
  }

  &.status-paid {
    background-color: #d1fae5;
    color: #065f46;
  }

  &.status-overdue {
    background-color: #fee2e2;
    color: #b91c1c;
  }
}

.invoice-items {
  margin: 1.5rem 0;
  overflow-x: auto;
  margin-bottom: 0;

  table {
    width: 100%;
    border-collapse: collapse;

    th {
      text-align: left;
      padding: 0.75rem;
      font-weight: 600;
      border-bottom: 1px solid #e5e7eb;
      // color: #4b5563;
      color: #000a1b;
      // font-size: 0.9rem;

      &:first-child {
        width: 45%;
      }

      &:not(:first-child) {
        text-align: right;
      }
    }

    td {
      padding: 0.75rem;
      border-bottom: 1px solid #e5e7eb;

      &:not(:first-child) {
        text-align: right;
      }

    }
  }
}

.invoice-summary {
  // margin-top: 1rem;
  display: flex;
  flex-direction: column;
  align-items: flex-end;

  .summary-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 250px;
    padding: 0.5rem 0;
  }

  .amount-due {
    width: 300px;
    // margin-top: 1rem;
    border-top: 2px solid #e5e7eb;
    padding-top: .75rem;
    padding-right: .75rem;
    padding-left: 3.5rem;

    h3 {
      font-size: 1rem;
      // color: #4b5563;
      color: #111827;
      font-weight: 700;
      // margin-bottom: 0.5rem;
    }

    h2 {
      font-size: 1;
      font-weight: 700;
      margin: 0;
      color: #111827;
    }
  }
}

.send-invoice-form {
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
  padding: 0.5rem 0;

  .form-field {
    display: flex;
    flex-direction: column;

    label {
      margin-bottom: 0.5rem;
      font-size: 0.9rem;
      color: #4b5563;
    }

    &.checkbox-field {
      flex-direction: row;
      align-items: center;
    }
  }
}

@media (max-width: 768px) {

  .invoice-header,
  .invoice-parties,
  .invoice-meta {
    flex-direction: column;
    gap: 1rem;
  }

  .header-actions {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;

    h1 {
      margin: 0;
    }
  }

  .action-buttons {
    width: 100%;
    // justify-content: space-between;
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  .invoice-summary {
    align-items: stretch;

    .summary-row,
    .amount-due {
      width: 100%;
    }
  }
}

/* Iframe styles for email content */
.email-iframe {
  width: 100%;
  min-height: 600px;
  border: none;
  background: white;
  border-radius: 4px;
}
</style>
