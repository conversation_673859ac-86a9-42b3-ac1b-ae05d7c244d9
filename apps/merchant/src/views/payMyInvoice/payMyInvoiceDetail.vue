<script setup lang="ts">
import { Format } from '@shared'
import dayjs from 'dayjs'
import ProgressSpinner from 'primevue/progressspinner'
import { onMounted, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRoute } from 'vue-router'
import { useDict } from '@/composables/useDict'
import { TransactionStatus } from '@/constants/transaction'
import { invoice as invoiceApi } from '@/services/api'
import { useCopy } from '@/utils/clipboard'
import { formatDate } from '@/utils/date'

const { t } = useI18n()

const copy = useCopy()

interface StatusColorMap {
  [key: number]: {
    separator: string
    text: string
  }
  default: {
    separator: string
    text: string
  }
}

const invoiceInfo = ref<Invoice.Info>({} as Invoice.Info)

const loading = ref(false)

const route = useRoute()

const resendInvoicePop = ref()

const resendTipsVisible = ref(false)

const resendFeedbackVisible = ref(false)

const tipsType = ref('SMS')

const toggleResendInvoicePop = (event: any) => {
  resendInvoicePop.value.toggle(event)
}
const openTipsDialog = (type: string) => {
  tipsType.value = type
  resendTipsVisible.value = true
}

const handleResendInvoice = () => {
  invoiceApi.sendInvoiceEmail({ id: Number(route.params.id) }).then((res) => {
    if (res.code === 0) {
      resendFeedbackVisible.value = true
    }
  })
}

const { getLabel: getPaymentMethodLabel } = useDict('invoice_timeline_status')

const STATUS_COLOR_MAP: StatusColorMap = {
  1: { separator: '#39b54a', text: '#39b54a' },
  2: { separator: '#eb001b', text: '#eb001b' },
  3: { separator: '#fe4c1c', text: '#fe4c1c' },
  default: {
    separator: '#fe4c1c',
    text: '#545454',
  },
}

const { getLabel: getTransStatusLabel } = useDict('trans_status')

const getTimelineSeparatorColor = (status: number) => {
  return STATUS_COLOR_MAP[status]?.separator || STATUS_COLOR_MAP.default.separator
}

const getTimelineTextColor = (status: number) => {
  return STATUS_COLOR_MAP[status]?.text || STATUS_COLOR_MAP.default.text
}

const columns = ref<TableColumnItem[]>([
  {
    field: 'date',
    header: 'Created At',
    style: { minWidth: '110px' },
    template: 'date',
  },
  {
    field: 'payment_amount',
    header: 'Amount',
    style: { minWidth: '110px' },
    template: 'payment_amount',
  },
  { field: 'payment_method', header: 'Payment Method', template: 'payment_method', style: { minWidth: '120px' } },
  {
    field: 'status',
    header: 'Status',
    style: { minWidth: '100px' },
    template: 'status',
  },
])

onMounted(() => {
  loading.value = true
  invoiceApi.getInvoiceDetail({ id: Number(route.params.id) }).then((res) => {
    invoiceInfo.value = res.data
  }).finally(() => {
    loading.value = false
  })
})
</script>

<template>
  <div class="invoice-container flex justify-between items-start">
    <div class="invoice-content bg-white rounded-lg w-3/4 mr-8 pb-10">
      <!-- Loading State -->
      <div v-if="loading" class="flex justify-center items-center h-96">
        <ProgressSpinner />
      </div>
      <!-- Content when loaded -->
      <div v-else>
        <div class="left-header flex justify-between items-start py-8 px-6">
          <div class="flex flex-col gap-2">
            <div class="flex items-center gap-1 xl:gap-4">
              <div class="font-bold text-3xl xl:text-3xl">
                A${{ invoiceInfo.sub_total }} {{ invoiceInfo.currency }}
              </div>
              <BaseTag type="paid" text="Succeeded" class="w-30" />
            </div>
            <div>
              Charged to
              <span class="underline text-[#fe4c1c] cursor-pointer">
                {{ invoiceInfo?.customer?.name }}
              </span>
            </div>
          </div>
          <div>
            <Button
              label="View Invoice" variant="outlined" @click="$router.push({
                name: 'payMyInvoiceInvoiceDetailAndSend',
                params: {
                  id: invoiceInfo.id,
                },
              })"
            />
          </div>
        </div>
        <div class="left-content pb-6 px-2">
          <Accordion value="0">
            <AccordionPanel value="0">
              <AccordionHeader>
                <span class="text-xl text-[#181349]">
                  TimeLine
                </span>
              </AccordionHeader>
              <AccordionContent>
                <Timeline
                  :value="invoiceInfo.timeline || []" class="customized-timeline p-4  mt-6"
                  :class="{ 'pb-16': invoiceInfo.timeline, 'pb-8': !invoiceInfo.timeline }"
                >
                  <template #marker="slotProps">
                    <span
                      class="flex items-center justify-center text-white rounded-full z-10 "
                      :style="{ backgroundColor: slotProps.item.color }"
                    >
                      <i
                        class="pi pi-circle-fill"
                        :class="[slotProps.item.icon, { 'rotate-45': slotProps.item.status === 'Pending Payment' }]"
                        :style="{ color: getTimelineSeparatorColor(slotProps.item.status) }"
                      />
                    </span>
                  </template>
                  <template #content="slotProps">
                    <div class="mt-4 relative custom-timeline">
                      <div
                        class="text-xl font-semibold "
                        :style="{ color: getTimelineTextColor(slotProps.item.status) }"
                      >
                        {{ getPaymentMethodLabel(slotProps.item.status) }}
                      </div>
                      <div :class="{ 'mt-2': slotProps.item.status !== 'Failed Payment' }">
                        {{ dayjs(slotProps.item.created_at).format('DD MMM YYYY hh:mm') }}
                      </div>
                      <div v-if="slotProps.item.reason">
                        Reason:{{ slotProps.item.reason }}
                      </div>
                      <div
                        v-if="getPaymentMethodLabel(slotProps.item.status) === 'Pending Payment'"
                        class="absolute  -right-86 top-2 resend-invoice-btn"
                      >
                        <Button v-if="[1, 2].includes(invoiceInfo.status)" label="RESEND INVOICE" severity="warn" @click="toggleResendInvoicePop" />
                        <Popover ref="resendInvoicePop" class="custom-resend-pop">
                          <div class="flex flex-col">
                            <!-- <div class="resend-item" @click="resendFeedbackVisible = true">
                              RESEND INVOICE
                            </div> -->
                            <!-- <div class="resend-item" @click="openTipsDialog('SMS')">
                              RESEND VIA SMS
                            </div> -->
                            <div class="resend-item" @click="openTipsDialog('EMAIL')">
                              RESEND VIA EMAIL
                            </div>
                          </div>
                        </Popover>
                        <Dialog
                          v-model:visible="resendTipsVisible" :dismissable-mask="true" :modal="true"
                          pt:root:class="!border-0 !bg-[#ffe3e8]" pt:mask:class="backdrop-blur-sm"
                        >
                          <template #container="{ closeCallback }">
                            <div class="w-76 h-84 p-10">
                              <div class="text-center text-xl font-bold text-[#181349]">
                                Are you sure you want to resend this invoice via <span>{{ tipsType }}</span> to the
                                customer?
                              </div>
                              <div class="mt-6 flex flex-col justify-center gap-4">
                                <Button
                                  label="YES,RESEND" severity="warn" class="btn-pop"
                                  @click="handleResendInvoice"
                                />
                                <Button label="CANCEL" class="btn-pop" @click="closeCallback" />
                              </div>
                            </div>
                          </template>
                        </Dialog>
                      </div>
                      <div class="content-background" />
                    </div>
                  </template>
                </Timeline>
              </AccordionContent>
            </AccordionPanel>
          </Accordion>

          <div class="mt-6 px-4">
            <div class="text-2xl font-bold text-(colors-primary) mb-4">
              Transaction
            </div>

            <BaseDataTable
              :value="invoiceInfo?.transaction || []" :columns="columns" :scrollable="true" :show-multiple-column="false"
              :paginator="false" :rows="20" :lazy="true" data-key="id" sort-mode="single"
              search-placeholder="Search" type-placeholder="Filter By"
              :show-search-bar="false"
            >
              <template #payment_amount="{ data }">
                {{ Format.formatAmount(data?.payment_amount) }}
              </template>
              <template #date="{ data }">
                {{ formatDate(data?.created_at) }}
              </template>
              <template #payment_method="{ data }">
                <BaseCardType
                  :text="data.customer_banking?.account_no" :card-type="data.customer_banking?.credit_brand"
                  :is-show-card-number="true"
                />
              </template>
              <template #customer="{ data }">
                <span v-if="data?.customer?.name" class="w-30">
                  {{ data?.customer?.name }}
                </span>
              </template>
              <template #status="{ data }">
                <BaseTag
                  :type="(data.status === TransactionStatus.SUCCEEDED ? 'paid'
                    : data.status === TransactionStatus.FAILED ? 'failed' : 'upcoming')"
                  :text="getTransStatusLabel(data.status)" class="w-30"
                />
              </template>
            </BaseDataTable>
          </div>
        </div>
      </div>
    </div>
    <div class="invoice-details bg-[#09deff] text-[#181349] rounded-lg px-6 py-6 w-1/4">
      <div v-if="false" class="flex justify-between items-center mb-4 detail-left-title">
        <div class="text-[28px] font-semibold my-2">
          Details
        </div>
        <Button variant="outlined" icon="pi pi-pen-to-square" label="Edit details" />
      </div>
      <div v-if="false" class="customer-details">
        <div class="details-edit">
          <div class="font-semibold mb-2">
            Payment ID
          </div>
          <div class="flex items-center gap-2 px-2 py-2 detail-left-item cursor-pointer" />
        </div>
        <div class="details-edit">
          <div class="font-semibold mb-2">
            Payment Method
          </div>
          <div class="flex items-center gap-2 px-2 py-1 detail-left-item cursor-pointer">
            <!-- <i class="pi pi-facebook" />s -->
            <!-- <span>**** 4545</span> -->
            <BaseCardType
              :text="invoiceInfo?.payment_method?.account_no"
              :card-type="invoiceInfo.payment_method?.credit_brand" :is-show-card-number="true"
            />
          </div>
        </div>
        <div class="details-edit">
          <div class="font-semibold mb-2">
            Plan Name
          </div>
          <div class="flex items-center gap-2 px-2 py-2 detail-left-item cursor-pointer">
            <!-- Lorem LLJsd -->
          </div>
        </div>
        <div class="details-edit">
          <div class="font-semibold mb-2">
            Statement Description
          </div>
          <div class="flex items-center gap-2 px-2 py-2 detail-left-item cursor-pointer">
            {{ invoiceInfo?.customer?.name }}
          </div>
        </div>
        <div class="details-edit">
          <div class="font-semibold mb-2">
            Last Updated
          </div>
          <div class="flex items-center gap-2  px-2 py-2 cursor-pointer detail-left-item" :title="t('common.copy')">
            <!-- <span>{{ data?.created_at }}</span> -->
            <!-- <span>{{ dayjs('2025-5-26').format('DD MMM,hh:mm') }}</span> -->
          </div>
        </div>
        <div class="details-edit">
          <div class="font-semibold mb-2">
            Language
          </div>
          <div class="flex items-center gap-2  px-2 py-2 cursor-pointer detail-left-item" :title="t('common.copy')">
            <!-- English(United States) -->
          </div>
        </div>
        <div class="details-edit">
          <div class="font-semibold mb-2">
            Risk Evaluation
          </div>
          <div class="flex items-center gap-2  px-2 py-2 cursor-pointer detail-left-item" :title="t('common.copy')">
            <!-- <i class="pi-shield pi" /> -->
            <!-- Normal -->
          </div>
        </div>
      </div>
      <div class="flex justify-between items-center mb-4 detail-left-title">
        <div class="text-[28px] font-semibold my-2">
          Customer
        </div>
      </div>
      <div>
        <div class="details-edit">
          <div class="font-semibold mb-2">
            Customer ID
          </div>
          <div class="flex items-center gap-2 px-2 py-2 detail-left-item cursor-pointer">
            <span @click="invoiceInfo?.customer?.customer_id && copy(invoiceInfo?.customer?.customer_id)">{{
              invoiceInfo?.customer?.customer_id }}</span>
          </div>
        </div>
        <div class="details-edit">
          <div class="font-semibold mb-2">
            Name
          </div>
          <div class="flex items-center gap-2 px-2 py-2 detail-left-item cursor-pointer">
            {{ invoiceInfo?.customer?.name }}
          </div>
        </div>
      </div>
    </div>
    <Dialog
      v-model:visible="resendFeedbackVisible" :dismissable-mask="true" :modal="true"
      pt:root:class="!border-0 !bg-[#ffe3e8]" pt:mask:class="backdrop-blur-sm"
    >
      <template #container="{ closeCallback }">
        <div class="w-94 h-70 p-10">
          <div class="text-center text-2xl font-bold text-[#181349] mb-6">
            Invoice Sent!
          </div>
          <div class="text-[#545454] text-xl text-center">
            The invoice has been successfully resent to the customer.
          </div>
          <div class="mt-10 flex  justify-center">
            <Button
              label="CLOSE" class="btn-pop" @click=" () => {
                resendFeedbackVisible = false
                closeCallback()
              }"
            />
          </div>
        </div>
      </template>
    </Dialog>
  </div>
</template>

<style scoped lang="scss">
@use '@/styles/mixins/breakpoints' as *;

.btn-pop {
  width: 200px;
  display: inline-block;
  padding: 1rem 1rem;
}
.invoice-container{
  @include media-breakpoint-down(xl) {
    flex-direction: column;
    gap: 2rem;
  }
}
.invoice-details {
  @include media-breakpoint-down(xxxl) {
    width: 34%;
  }
@include media-breakpoint-down(xl) {
    width: 100%;
  }
}

.invoice-content {
  @include media-breakpoint-down(xxxl) {
    width: 66%;
  }
  @include media-breakpoint-down(xl) {
    width: 100%;
  }
}

.resend-invoice-btn{
  @include media-breakpoint-down(sm) {
    right: -250px;
  }
  @media (max-width:468px) {
    right: -220px;
  }
}

.detail-left-title {
  padding-bottom: .5rem;
  border-bottom: 1px solid #fff;
}

.details-edit {
  margin-bottom: 15px;
  --p-tag-primary-background: #fff;
}

.detail-left-item {
  background-color: #ffffff;
  border-radius: 8px;
  min-height: 28px;
}

.left-content {
  --p-accordion-header-color: #181349;
  --p-accordion-header-font-weight: 700;
  --p-accordion-content-border-color: #ffffff;
}

.customized-timeline {
  --p-timeline-event-connector-color: #ff5f00;
  --p-timeline-event-min-height: 6rem;

}

:deep(.customized-timeline .p-timeline-event-opposite) {
  display: none;
}

:deep(.customized-timeline) {
  .p-timeline-event-separator {
    position: relative;
    z-index: 4;
  }

  .p-timeline-event-content {
    position: absolute;
    top: -28px;
    left: 20px;
  }

}

.custom-timeline {
  position: relative;
  z-index: 3;

  .content-background {

    position: absolute;
    height: 70px;
    width: 320px;
    background-color: var(--color-white-100);
    border-radius: 10px;
    z-index: -1;
    top: -8px;
    left: -50px;
    @include media-breakpoint-down(sm) {
    width: 260px;
  }
  }

}

.resend-item {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 10px 20px;
  color: #545454;
  border-radius: 10px;

  position: relative;

  &:not(:last-child) {
    &::after {
      position: absolute;
      content: '';
      height: 1px;
      background-color: #545454;
      width: 95%;
      bottom: 0;
      left: 5px;
    }

  }

  &:hover {
    background-color: #ff5f00;
    color: #ffffff;
  }
}
</style>

<style>
.custom-resend-pop.p-popover {
  --p-popover-content-padding: 0;
  --p-popover-border-radius: 10px;
}
</style>
