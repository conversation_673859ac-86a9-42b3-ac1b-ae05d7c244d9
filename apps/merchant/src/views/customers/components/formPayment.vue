<script setup lang="ts">
import { toTypedSchema } from '@vee-validate/zod'
import { Field, Form as VeeForm } from 'vee-validate'
import { ref } from 'vue'
import { z } from 'zod'
import { country as countryApi } from '@/services/api'
import { useCheckoutStore } from '@/store/modules/checkout'

const store = useCheckoutStore()

const formRef = ref()

const schema = toTypedSchema(z.object({
  cardNumber: z.string()
    .min(13, 'Card number must be between 13 and 19 digits')
    .max(19, 'Card number must be between 13 and 19 digits')
    .refine((value) => {
      const cleanValue = value.replace(/\D/g, '')
      let sum = 0
      let shouldDouble = false
      for (let i = cleanValue.length - 1; i >= 0; i--) {
        let digit = Number.parseInt(cleanValue.charAt(i))
        if (shouldDouble) {
          digit *= 2
          if (digit > 9) { digit -= 9 }
        }
        sum += digit
        shouldDouble = !shouldDouble
      }
      return sum % 10 === 0
    }, 'Invalid card number'),
  expiryDate: z.string()
    .regex(/^\d{2}\/\d{2}$/, 'Expiry date must be in MM/YY format')
    .refine((value) => {
      const [month, year] = value.split('/')
      const currentDate = new Date()
      const currentYear = currentDate.getFullYear() % 100
      const currentMonth = currentDate.getMonth() + 1

      const expiryMonth = Number.parseInt(month)
      const expiryYear = Number.parseInt(year)

      if (expiryMonth < 1 || expiryMonth > 12) {
        return false
      }

      return !(expiryYear < currentYear || (expiryYear === currentYear && expiryMonth < currentMonth))
    }, 'Card has expired or invalid date'),
  securityCode: z.string()
    .regex(/^\d{3,4}$/, 'Security code must be 3 or 4 digits'),
  nameOnCard: z.string()
    .min(2, 'Name must be at least 2 characters')
    .refine(value => value.includes(' '), 'Please enter your full name as it appears on the card'),
  email: z.string().email('Invalid email'),

  // 账单地址信息
  country: z.string().min(1, 'Country/Region is required'),
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().min(1, 'Last name is required'),
  company: z.string().optional(),
  address: z.string().min(1, 'Address is required'),
  addressLine2: z.string().optional(),
  city: z.string().min(1, 'City is required'),
  state: z.string().min(1, 'State/territory is required'),
  postcode: z.string().min(1, 'Postcode is required'),
  phone: z.string().min(1, 'Phone is required'),
}))

const isGetCountryLoading = ref(false)

// 国家/地区选项
const countries = ref()

// 州/领地选项
const stateOptions = ref()

// 暴露方法给父组件
const validate = async () => {
  const result = await formRef.value?.validate()
  return result
}

const resetForm = () => {
  formRef.value?.resetForm()
}

const submitForm = async () => {
  const result = await validate()
  if (result.valid) {
    // 保存账单地址到 store
    store.setBillingAddress({
      country: result.values.country,
      first_name: result.values.firstName,
      last_name: result.values.lastName,
      company: result.values.company,
      address_line1: result.values.address,
      address_line2: result.values.addressLine2,
      city: result.values.city,
      state: result.values.state,
      postal_code: result.values.postcode,
      phone: result.values.phone,
    })

    return result.values
  }
  return false
}

const getCurrentValues = () => {
  return formRef.value?.values
}

const getCountry = async () => {
  isGetCountryLoading.value = true
  const { code, data } = await countryApi.getList()
  if (code === 0) {
    countries.value = data.map(item => ({
      name: item.name,
      id: item.id,
      iso2: item.iso2,
      state: item.state,
    }))

    if (store.billingAddress.country) {
      setCountry(store.billingAddress.country, true)
    }
  }
  isGetCountryLoading.value = false
}

const setCountry = (value: string, isInit = false) => {
  if (!isInit) {
    store.billingAddress.state = ''
  }
  const country = countries.value.find((item: any) => item.iso2 === value)
  if (country && country.state && country.state.length > 0) {
    stateOptions.value = country?.state
  }
  else {
    stateOptions.value = []
  }
}

getCountry()

defineExpose({
  validate,
  resetForm,
  submitForm,
  getCurrentValues,
})
</script>

<template>
  <div>
    <!-- 合并后的表单 -->
    <VeeForm
      ref="formRef"
      :validation-schema="schema"
      class="space-y-6"
      @submit="submitForm"
    >
      <!-- 信用卡表单部分 -->
      <span class="text-gray-600 mb-4 italic">
        All fields are required unless marked as optional.
      </span>
      <!-- Card Number -->
      <Field v-slot="{ field, errorMessage }" v-model="store.paymentDetails.cardNumber" name="cardNumber">
        <div class="field mb-4 mt-4">
          <InputText v-bind="field" placeholder="Card Number" class="w-full" />
          <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
            {{ errorMessage }}
          </Message>
        </div>
      </Field>

      <div class="form-row mb-4">
        <!-- Expiry Date -->
        <Field v-slot="{ field, errorMessage }" v-model="store.paymentDetails.expiryDate" as="div" name="expiryDate" class="form-col">
          <div class="field">
            <InputMask :model-value="field.value" placeholder="Expiration date (MM/YY)" mask="99/99" class="w-full" />
            <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
              {{ errorMessage }}
            </Message>
          </div>
        </Field>

        <!-- Security Code -->
        <Field v-slot="{ field, errorMessage }" v-model="store.paymentDetails.securityCode" as="div" name="securityCode" class="form-col">
          <div class="field">
            <InputText v-bind="field" placeholder="Please enter the CVV(Security code)" class="w-full" />
            <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
              {{ errorMessage }}
            </Message>
          </div>
        </Field>
      </div>

      <!-- Name on Card -->
      <Field v-slot="{ field, errorMessage }" v-model="store.paymentDetails.nameOnCard" name="nameOnCard">
        <div class="field mb-4">
          <InputText v-bind="field" placeholder="Name on card" class="w-full" />
          <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
            {{ errorMessage }}
          </Message>
        </div>
      </Field>

      <!-- 账单地址表单部分 -->
      <div class="billing-address-form mt-6">
        <h3 class="text-xl font-semibold mb-4">
          Billing address
        </h3>
        <p class="text-gray-600 mb-4">
          Select the address that matches your card or payment method.
        </p>

        <!-- Country/Region -->
        <div class="field mb-4">
          <Field v-slot="{ field, errorMessage, handleChange }" v-model="store.billingAddress.country" name="country">
            <Select
              id="country"
              v-model="field.value"
              class="w-full"
              :options="countries"
              option-label="name"
              option-value="iso2"
              placeholder="Select country"
              :loading="isGetCountryLoading"
              filter
              show-clear
              @update:model-value="e => {
                handleChange(e)
                setCountry(e)
              }"
            />
            <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
              {{ errorMessage }}
            </Message>
          </Field>
        </div>

        <!-- Name fields (first name and last name) -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div class="field">
            <Field v-slot="{ field, errorMessage }" v-model="store.billingAddress.first_name" name="firstName">
              <InputText
                v-bind="field"
                id="firstName"
                class="w-full"
                placeholder="First name"
              />
              <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
                {{ errorMessage }}
              </Message>
            </Field>
          </div>

          <div class="field">
            <Field v-slot="{ field, errorMessage }" v-model="store.billingAddress.last_name" name="lastName">
              <InputText
                v-bind="field"
                id="lastName"
                class="w-full"
                placeholder="Last name"
              />
              <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
                {{ errorMessage }}
              </Message>
            </Field>
          </div>
        </div>

        <!-- Company (optional) -->
        <div class="field mb-4">
          <Field v-slot="{ field }" v-model="store.billingAddress.company" name="company">
            <InputText
              v-bind="field"
              id="company"
              class="w-full"
              placeholder="Company (optional)"
            />
          </Field>
        </div>

        <!-- Address -->
        <div class="field mb-4">
          <Field v-slot="{ field, errorMessage }" v-model="store.billingAddress.address_line1" name="address">
            <InputText
              v-bind="field"
              id="address"
              class="w-full"
              placeholder="Address"
            />
            <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
              {{ errorMessage }}
            </Message>
          </Field>
        </div>

        <!-- Address line 2 (optional) -->
        <div class="field mb-4">
          <Field v-slot="{ field }" v-model="store.billingAddress.address_line2" name="addressLine2">
            <InputText
              v-bind="field"
              id="addressLine2"
              class="w-full"
              placeholder="Address line 2 (optional)"
            />
          </Field>
        </div>

        <!-- City, State, Postcode -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
          <div class="field">
            <Field v-slot="{ field, errorMessage }" v-model="store.billingAddress.city" name="city">
              <InputText
                v-bind="field"
                id="city"
                class="w-full"
                placeholder="City"
              />
              <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
                {{ errorMessage }}
              </Message>
            </Field>
          </div>

          <div class="field">
            <Field v-slot="{ field, errorMessage, handleChange }" v-model="store.billingAddress.state" name="state">
              <Select
                id="state"
                class="w-full"
                :model-value="field.value"
                :options="stateOptions"
                option-label="name"
                option-value="name"
                placeholder="Select state"
                :loading="isGetCountryLoading"
                :disabled="!store.billingAddress.country"
                filter
                @update:model-value="handleChange"
              />
              <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
                {{ errorMessage }}
              </Message>
            </Field>
          </div>

          <div class="field">
            <Field v-slot="{ field, errorMessage }" v-model="store.billingAddress.postal_code" name="postcode">
              <InputText
                v-bind="field"
                id="postcode"
                class="w-full"
                placeholder="Postcode"
              />
              <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
                {{ errorMessage }}
              </Message>
            </Field>
          </div>
        </div>

        <!-- Phone -->
        <div class="field mb-4">
          <Field v-slot="{ field, errorMessage }" v-model="store.billingAddress.phone" name="phone">
            <InputText
              v-bind="field"
              id="phone"
              class="w-full"
              placeholder="Phone (required)"
            />
            <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
              {{ errorMessage }}
            </Message>
          </Field>
        </div>

        <!-- email -->
        <Field v-slot="{ field, errorMessage }" v-model="store.paymentDetails.email" name="email">
          <div class="field mb-4">
            <InputText v-bind="field" placeholder="Billing Email" class="w-full" />
            <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
              {{ errorMessage }}
            </Message>
          </div>
        </Field>
      </div>
    </VeeForm>
  </div>
</template>

<style scoped lang="scss">
.form-row {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.form-col {
  flex: 1;
  min-width: 250px; /* Ensures columns don't get too narrow */
}

.w-full {
  width: 100%;
}

.billing-address-form {
  h3 {
    color: #1e293b;
  }

  .field {
    label {
      font-weight: 500;
      color: #374151;
    }
  }

  :deep(.p-dropdown),
  :deep(.p-inputtext) {
    width: 100%;
    border-radius: 0.375rem;
    transition: all 0.2s ease;

    &:focus {
      box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.25);
    }
  }

  :deep(.p-dropdown-panel) {
    .p-dropdown-items {
      .p-dropdown-item {
        padding: 0.75rem 1rem;
        color: #374151;

        &:hover {
          background-color: #f3f4f6;
        }

        &.p-highlight {
          background-color: #eff6ff;
          color: #3b82f6;
        }
      }
    }
  }

  :deep(.p-input-icon-right) {
    width: 100%;

    i {
      right: 0.75rem;
      color: #9ca3af;
    }
  }
}

/* Responsive styles */
@media screen and (max-width: 768px) {
  .form-row {
    flex-direction: column;
    gap: 1rem;
  }

  .form-col {
    min-width: 100%;
  }
}

/* Small mobile devices */
@media screen and (max-width: 480px) {
  :deep(.p-float-label) {
    font-size: 0.9rem;
  }

  :deep(.p-inputtext) {
    font-size: 0.9rem;
    padding: 0.5rem;
  }
}
</style>
