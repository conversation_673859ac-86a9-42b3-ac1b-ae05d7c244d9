<script setup lang="ts">
import Button from 'primevue/button'
import Dialog from 'primevue/dialog'
import { reactive, ref, watch } from 'vue'
import { pickColor } from '@/views/communication/theme'
import Edit from '../../communication/component/edit.vue'
import emailPreview from '../../communication/component/emailPreview.vue'

const stateFormData = defineModel('formData', {
  type: Object,
  default: () => ({
    logo: [],
    theme: 'fe4c1c',
  }),
})

const formData = reactive<{
  logo: FileUpload.UploadFileItem[]
  theme: string
}>({
  logo: stateFormData.value.logo || [],
  theme: stateFormData.value.theme || '#fe4c1c',
})

const editRef = ref<InstanceType<typeof Edit>>()

// 对话框显示状态
const showSettingsDialog = ref(false)

// 保存设置（Logo和Theme）
const saveSettings = () => {
  formData.theme = headlineColor.value
  stateFormData.value.logo = formData.logo
  stateFormData.value.theme = headlineColor.value
  showSettingsDialog.value = false
}

// 打开设置对话框
const handleEditSettings = () => {
  showSettingsDialog.value = true
}

const headlineColor = ref('#fe4c1c')
const updateSelectedColor = (color: string) => {
  headlineColor.value = color
}

watch(() => stateFormData.value, () => {
  formData.logo = stateFormData.value.logo || []
  formData.theme = stateFormData.value.theme || '#fe4c1c'
}, { deep: true })
</script>

<template>
  <div class="create-invite-confirm">
    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
      <!-- Email Preview Section -->
      <!-- <div class="email-container">
        <div class="flex justify-between items-center mb-3">
          <div class="font-medium text-gray-700">
            What your customer sees <span class="font-bold text-gray-900">the email</span>
          </div>
          <div class="flex space-x-2">
            <Button
              class="items-center" severity="secondary" icon="pi pi-pencil" label="Edit"
              @click="handleEditSettings"
            />
          </div>
        </div> -->

      <!-- <div class="email-preview bg-white rounded-lg overflow-hidden shadow-md"> -->
      <!-- Browser Address Bar -->
      <!-- <div class="bg-gray-900 py-2 px-3 flex items-center">
            <div class="flex items-center space-x-2">
              <div class="flex space-x-1">
                <div class="w-2.5 h-2.5 rounded-full bg-red-500" />
                <div class="w-2.5 h-2.5 rounded-full bg-yellow-500" />
                <div class="w-2.5 h-2.5 rounded-full bg-green-500" />
              </div>
            </div>
            <div class="ml-4 bg-gray-800 rounded-full text-gray-300 text-xs py-1 px-3 flex items-center">
              <i class="pi pi-lock text-green-400 mr-1" />
              <span>https://emailclient.com/preview</span>
            </div>
          </div> -->

      <!-- Email Content -->
      <!-- <div class="flex flex-col items-center"> -->
      <!-- Logo部分 - 白色背景 -->
      <!-- <div class="w-full bg-white p-6 flex justify-center">
              <div class="mb-4 cursor-pointer" @click="handleEditSettings">
                <img
                  :src="logoPath" alt="Company Logo" class="w-24 h-24 object-contain"
                  onerror="this.src='data:image/svg+xml;charset=UTF-8,%3Csvg%20width%3D%2220%22%20height%3D%2220%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Crect%20x%3D%220%22%20y%3D%220%22%20width%3D%2220%22%20height%3D%2220%22%20fill%3D%22%23e2e8f0%22%2F%3E%3C%2Fsvg%3E'"
                >
              </div>
            </div> -->

      <!-- 内容部分 - 灰色背景 -->
      <!-- <div class="w-full bg-gray-100 p-8 flex flex-col items-center"> -->
      <!-- 辅助灰色线条 -->
      <!-- <div class="flex flex-col items-center space-y-2 mb-6 w-full max-w-md">
                <div class="h-2 w-full bg-gray-300 rounded" />
                <div class="h-2 w-3/5 bg-gray-300 rounded" />
              </div> -->

      <!-- CTA Button -->
      <!-- <div class="w-64">
                <button
                  :style="{
                    'background-color': `#${theme}`,
                  }"
                  class="w-full py-3 px-4 rounded-full text-white font-medium transition-all shadow-sm flex items-center"
                >
                  <span class="w-3/4 h-1 bg-white rounded-full mx-auto" />
                  <i class="pi pi-arrow-right text-white ml-2" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div> -->
      <div>
        <div class="edit-btn flex justify-end">
          <Button
            class="items-center" severity="secondary" icon="pi pi-pencil" label="Edit"
            @click="handleEditSettings"
          />
        </div>
        <emailPreview :header-style="pickColor(formData.theme)" :logo-src="formData.logo" @click="handleEditSettings" />
      </div>

      <!-- SMS Preview Section - 暂时隐藏 -->
      <!--
      <div class="sms-container">
        <div class="flex justify-between items-center mb-3">
          <div class="text-sm font-medium text-gray-700">
            What your customer sees <span class="font-bold text-gray-900">SMS</span>
          </div>
          <button
            class="edit-btn flex items-center"
            @click="handleEditSMS"
          >
            <i class="pi pi-pencil mr-1" />
            <span>Edit</span>
          </button>
        </div>

        <div class="sms-preview rounded-lg overflow-hidden shadow-md">
          <div class="phone-header bg-gray-800 text-white px-4 py-2 text-center text-xs">
            <div class="flex justify-between items-center">
              <span>9:41 AM</span>
              <div class="flex space-x-1">
                <i class="pi pi-signal" />
                <i class="pi pi-wifi" />
                <i class="pi pi-battery" />
              </div>
            </div>
          </div>

          <div class="phone-content bg-gray-100 p-6">
            <div class="text-center mb-6 text-gray-600 text-sm">
              {{ phoneNumber }}
            </div>

            <div class="flex flex-col">
              <div class="message-bubble self-end max-w-xs" :style="{ backgroundColor: theme.primaryColor }">
                <p class="text-white text-sm">
                  {{ smsContent }}
                </p>
              </div>

              <div class="text-xs text-gray-500 text-center mt-2">
                Now
              </div>
            </div>
          </div>
        </div>
      </div>
      -->
    </div>

    <!-- 设置对话框（Logo和Theme合并） -->
    <Dialog
      v-model:visible="showSettingsDialog" header="Email Settings" :style="{ width: '820px' }" :modal="true"
      class="settings-dialog"
    >
      <div class="p-4">
        <!-- <div class="">
          <h3 class="text-lg font-medium text-gray-800 mb-3">
            Logo
          </h3>
          <div class="mb-4 flex justify-center">
            <BaseFileUpload v-model:model-value="formData.logo" :multiple="false" :max-files="1" accept="image/*" />
          </div>
          <div>
            <h3 class="text-lg font-medium text-gray-800 mb-3">
              Theme Colors
            </h3>
            <ColorPicker v-model="formData.theme" format="hex" />
          </div>

          <div class="flex justify-end space-x-2 pt-4 gap-2">
            <Button label="Cancel" severity="secondary" @click="showSettingsDialog = false" />
            <Button label="Save" @click="saveSettings" />
          </div>
        </div> -->
        <Edit ref="editRef" v-model:logo="formData.logo" :is-show-tip="false" mode="normal" @update:selected-color="updateSelectedColor" />
        <div class="flex justify-end space-x-2 pt-4 gap-2">
          <Button label="Cancel" severity="secondary" @click="showSettingsDialog = false" />
          <Button label="Save" severity="warn" @click="saveSettings" />
        </div>
      </div>
    </Dialog>
  </div>
</template>

<style lang="scss" scoped>
.create-invite-confirm {

  .email-preview {
    border: 1px solid #e5e7eb;
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    }
  }

  .sms-preview {
    border: 1px solid #e5e7eb;
    height: 340px;

    .phone-content {
      height: 290px;
      overflow-y: auto;
    }

    .message-bubble {
      padding: 0.75rem 1rem;
      border-radius: 1rem 0 1rem 1rem;
      margin-bottom: 0.25rem;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    }
  }

  .logo-container {
    position: relative;
    display: inline-block;

    .logo-wrapper {
      border: 2px solid;
      transition: all 0.2s ease;
      overflow: hidden;
      background-color: white;
    }

    .edit-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      transition: opacity 0.2s ease;
    }

    &:hover {
      .logo-wrapper {
        transform: scale(1.02);
      }
    }
  }

  .send-email-btn {
    background-color: #1e40af;
    color: white;
    padding: 0.625rem 1.25rem;
    border-radius: 0.375rem;
    font-weight: 500;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;

    &:hover {
      background-color: #1e3a8a;
      transform: translateY(-1px);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
  }

  // 对话框样式
  :deep(.settings-dialog) {
    .p-dialog-header {
      background-color: #f9fafb;
      border-bottom: 1px solid #e5e7eb;
      padding: 1rem 1.5rem;
    }

    .p-dialog-content {
      padding: 0;
    }

    .p-colorpicker {
      width: 2.5rem;
      height: 2.5rem;
    }
  }
}
</style>
