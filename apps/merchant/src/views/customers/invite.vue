<script setup lang="ts">
import type { PlanSubmitData } from '../planSubscription/composables/usePlanForm'
import { toTypedSchema } from '@vee-validate/zod'
import Skeleton from 'primevue/skeleton'
import { Field, Form as VeeForm } from 'vee-validate'
import { nextTick, onMounted, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRoute } from 'vue-router'
import { z } from 'zod'
import { useCheckoutStore } from '@/store/modules/checkout'
import formBankAccountDetails from './components/formBankAccountDetails.vue'
import formPayment from './components/formPayment.vue'
import SubscriptionFormPreview from './components/subscriptionFormPreview.vue'

const props = withDefaults(defineProps<{
  customerId?: string
  readonly?: boolean
}>(), {
  customerId: '',
  readonly: false,
})

const schema = toTypedSchema(z.object({
  customer_name: z.string().min(2, 'Name must be at least 2 characters'),
  customer_phone: z.string(),
}))

const route = useRoute()

const store = useCheckoutStore()

const { t } = useI18n()
const formRef = ref()
const formBankAccountDetailsRef = ref<InstanceType<typeof formBankAccountDetails>>()
const formPaymentRef = ref<InstanceType<typeof formPayment>>()

// 高度动画的JavaScript钩子
const beforeEnter = (el: Element) => {
  (el as HTMLElement).style.height = '0';
  (el as HTMLElement).style.opacity = '0';
  (el as HTMLElement).style.overflow = 'hidden'
}

const enter = (el: Element, done: () => void) => {
  const htmlEl = el as HTMLElement
  const height = htmlEl.scrollHeight
  htmlEl.style.height = '0'
  htmlEl.style.opacity = '0'

  // 强制回流
  void htmlEl.offsetHeight

  htmlEl.style.transition = 'height 0.5s ease-in-out, opacity 0.2s ease-in-out'
  htmlEl.style.height = `${height}px`
  htmlEl.style.opacity = '1'

  htmlEl.addEventListener('transitionend', function onEnd(event) {
    if (event.propertyName === 'height') {
      htmlEl.style.height = 'auto'
      htmlEl.style.overflow = 'visible'
      htmlEl.removeEventListener('transitionend', onEnd)
      done()
    }
  })
}

const leave = (el: Element, done: () => void) => {
  const htmlEl = el as HTMLElement
  htmlEl.style.height = `${htmlEl.scrollHeight}px`
  htmlEl.style.overflow = 'hidden'

  // 强制回流
  void htmlEl.offsetHeight

  htmlEl.style.transition = 'height 0.5s ease-in-out, opacity 0.2s ease-in-out'
  htmlEl.style.height = '0'
  htmlEl.style.opacity = '0'

  htmlEl.addEventListener('transitionend', function onEnd(event) {
    if (event.propertyName === 'height') {
      htmlEl.removeEventListener('transitionend', onEnd)
      done()
    }
  })
}

const currencies = [
  { code: 'AUD', label: 'AUD' },
]

const redirectToHome = () => {
  window.location.href = window.location.origin
}

const handleSubmit = async () => {
  // 验证基本信息表单
  const { valid } = await formRef.value?.validate()

  if (valid) {
    // 根据支付方式验证对应表单
    let paymentFormValid = false

    if (store.activePaymentMethod === 'card') {
      const result = await formBankAccountDetailsRef.value?.submitForm()
      if (result) {
        paymentFormValid = true
      }
    }
    else if (store.activePaymentMethod === 'bankAccount') {
      const result = await formPaymentRef.value?.submitForm()
      if (result) {
        paymentFormValid = true
      }
    }

    if (!paymentFormValid) {
      return
    }

    const data = await store.submitInvite()

    if (data?.success) {
      const div = document.createElement('div')
      div.innerHTML = data.html

      document.body.appendChild(div)

      nextTick(() => {
        const stepUpForm = document.querySelector('#step-up-form') as HTMLFormElement
        if (stepUpForm) {
          stepUpForm?.submit()
        }
      })
    }
  }
}

const resetForm = () => {
  store.resetForm()
  formRef.value?.resetForm()
  formBankAccountDetailsRef.value?.resetForm()
  formPaymentRef.value?.resetForm()
}

onMounted(() => {
  if (props.readonly) {
    resetForm()
  }
  if (route.query.status === '0') {
    store.setPageStatus('success')
  }
  else if (route.query.status === '1') {
    store.pageErrorMessage = 'Sorry Payment Failed'
    store.setPageStatus('error')
  }
  else {
    store.setCustomerId(props.customerId || String(route.params.id) || '')
  }
})
</script>

<template>
  <div class="checkout-page" :style="store.cssVariables">
    <div class="checkout-container" :class="{ 'bg-transparent': store.pageStatus !== 'loading' }">
      <!-- Loading State -->
      <div v-if="store.pageStatus === 'loading'" class="loading-state">
        <div class="checkout-header mb-6">
          <Skeleton width="4rem" height="4rem" class="mb-4" />
          <Skeleton width="70%" height="2rem" class="mb-2" />
          <Skeleton width="50%" height="1.5rem" class="mb-4" />

          <div class="currency-selector mt-4">
            <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <Skeleton width="30%" height="1.5rem" />
              <Skeleton width="20%" height="2rem" />
            </div>
          </div>
        </div>

        <Card>
          <template #content>
            <div class="space-y-4">
              <Skeleton width="100%" height="3rem" class="mb-4" />
              <Skeleton width="100%" height="3rem" class="mb-4" />
              <Skeleton width="100%" height="3rem" class="mb-4" />

              <div class="payment-methods">
                <div class="flex gap-4 mb-4">
                  <Skeleton width="50%" height="3rem" />
                  <Skeleton width="50%" height="3rem" />
                </div>
                <Skeleton width="100%" height="12rem" />
              </div>
            </div>
          </template>
        </Card>
      </div>

      <!-- Success State -->
      <div v-else-if="store.pageStatus === 'success'" class="result-state success">
        <Card>
          <template #content>
            <div class="text-center py-8">
              <i class="!text-6xl pi pi-check-circle text-green-500 mb-4" />
              <h2 class="text-2xl font-semibold text-gray-900 mb-2">
                {{ t('checkout.success.title') }}
              </h2>
              <p class="text-gray-600 mb-6">
                {{ t('checkout.success.message') }}
              </p>
              <p class="text-gray-600 mb-4">
                {{ t('checkout.success.redirect', { seconds: store.redirectCountdown }) }}
              </p>
              <Button :label="t('checkout.success.redirectButton')" class="p-button-primary" @click="redirectToHome" />
            </div>
          </template>
        </Card>
      </div>

      <!-- Error State -->
      <div v-else-if="store.pageStatus === 'error'" class="result-state error">
        <Card>
          <template #content>
            <div class="text-center py-8">
              <i class="pi pi-exclamation-circle text-6xl text-red-500 mb-4" />
              <h2 class="text-2xl font-semibold text-gray-900 mb-2">
                Setup Failed
              </h2>
              <p class="text-gray-600 mb-6">
                {{ store.pageErrorMessage }}
              </p>
              <div class="flex justify-center gap-3">
                <Button label="Back" class="p-button-secondary" @click="$router.push('/customers')" />
                <Button label="Retry" @click="resetForm" />
              </div>
            </div>
          </template>
        </Card>
      </div>

      <!-- Normal Form State -->
      <div v-else>
        <!-- Header Section -->
        <div class="checkout-header mb-6">
          <div class="merchant-logo flex items-center justify-center">
            <img :src="store.config.logo" alt="Merchant Logo" class="max-w-40 max-h-40">
          </div>
          <div class="header-content-merchant" :style="store.config.headerStyle">
            <span class="mr-2">Setup a</span>
            <h2>Direct Debit Request with {{ store.config?.merchant_name }} </h2>
          </div>
        </div>
        <div class="checkout-content">
          <div class="header-content mt-4">
            <!-- Currency Selector -->
            <div class="currency-selector">
              <p class="currency-selector-description">
                {{ t('checkout.description') }}
              </p>
              <div class="flex items-center justify-between py-4">
                <div class="flex items-center">
                  <img src="@/assets/merchant/card-type/carte-blanche.png" alt="carte-blanche" class="w-8 w-8">
                </div>
                <div class="currency-label flex items-center gap-2">
                  {{ t('checkout.currency.label') }} ($)
                  <Select
                    v-model="store.selectedCurrency" :options="currencies" option-label="label"
                    option-value="code" class="currency-dropdown" :placeholder="t('checkout.currency.select')"
                  />
                </div>
              </div>
            </div>
          </div>

          <div class="payment-form">
            <div class="card-title">
              {{ t('checkout.basicInformation') }}
            </div>
            <VeeForm ref="formRef" :validation-schema="schema" @submit="handleSubmit">
              <div class="flex gap-4">
                <Field
                  v-slot="{ field, errorMessage }" v-model="store.customerInfo.customer_name" as="div"
                  class="w-1/2" name="customer_name"
                >
                  <label class="mb-2 block">Name</label>
                  <InputText
                    v-bind="field" id="name" :placeholder="t('checkout.form.name.placeholder')"
                    class="w-full"
                  />
                  <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
                    {{ errorMessage }}
                  </Message>
                </Field>

                <Field
                  v-slot="{ field, errorMessage }" v-model="store.customerInfo.customer_phone"
                  name="customer_phone" as="div" class="w-1/2"
                >
                  <label class="mb-2 block">Phone Number</label>
                  <InputText
                    id="phoneNumber" v-bind="field" :placeholder="t('checkout.form.phoneNumber.placeholder')"
                    class="w-full"
                  />
                  <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
                    {{ errorMessage }}
                  </Message>
                </Field>
              </div>
              <Field
                v-slot="{ field, errorMessage }" v-model="store.customerInfo.customer_email" name="customer_email"
                class="mt-4" as="div"
              >
                <label class="mb-2 block">Email</label>
                <InputText
                  v-bind="field" id="email" :placeholder="t('checkout.form.email.placeholder')" class="w-full"
                  disabled
                />
                <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
                  {{ errorMessage }}
                </Message>
              </Field>
            </VeeForm>

            <div class="card-title mt-8">
              Subscription Details
            </div>

            <SubscriptionFormPreview
              v-if="store.customerInfo?.customer_plan?.plan_id"
              :name="store.customerInfo?.customer_name"
              :form-data="(store.customerInfo?.customer_plan as PlanSubmitData)"
              :surcharge-rate="store.customerInfo?.customer_plan?.surcharge_rate"
              :unit-quantity="store.customerInfo?.customer_plan?.units || 1" :is-show-forecast="false"
              :start-date="store.customerInfo?.customer_plan?.start_date"
            />
            <template
              v-if="store.customerInfo.customer_bankings?.length && store.customerInfo?.customer_bankings.length > 0 && !store.isConfirmPaymentMethod"
            >
              <div class="pb-4">
                <div class="card-title" style="margin-block: 24px 8px;">
                  Payment Method
                </div>
                <p class="mb-4 font-bold text-gray-600">
                  This is your existing payment method:
                </p>
                <div
                  v-for="bank in store.customerInfo?.customer_bankings" :key="bank.id" class="bank-card"
                  :class="{ active: store.selectedBank === bank.id }" @click="store.setSelectedBank(bank.id)"
                >
                  <BaseCardType :card-type="bank.credit_brand" :text="bank.account_no" :is-show-card-number="true" />
                </div>
              </div>
              <div class="bg-gray-50 p-2 px-4 rounded-lg mb-4">
                <p class="font-bold py-3 text-gray-600">
                  Do you want to change to a new payment method?
                </p>
                <div class="flex justify-between gap-2">
                  <Button
                    label="YES, change my payment method" severity="primary"
                    :disabled="store.isSelectedBankLoading" @click="store.cancelPaymentMethod()"
                  />
                  <Button
                    label="NO, use selected card" severity="secondary" :loading="store.isSelectedBankLoading"
                    @click="store.confirmPaymentMethod()"
                  />
                </div>
              </div>
              <divide class="support-divide" />
            </template>

            <template v-else>
              <divide class="support-divide" :color="store.config.headerStyle.backgroundColor" />
              <p class="mb-6 font-bold text-gray-600">
                You don’t have an existing payment method. Please add your bank details:
              </p>
              <div class="card-title" style="margin-block: 24px 8px;">
                Payment Method
              </div>
              <span class="card-description">
                All transactions are secure and encrypted.
              </span>

              <div class="payment-method-wrap">
                <div
                  v-if="store.config.open_banking_transactions" class="payment-method-title flex items-center"
                  :class="[{ active: store.activePaymentMethod === 'bankAccount' }]"
                  :style="store.activePaymentMethod === 'bankAccount' ? store.config.headerStyle : {}"
                  @click="store.setActivePaymentMethod('bankAccount')"
                >
                  <RadioButton
                    class="mr-2" :model-value="store.activePaymentMethod === 'bankAccount'"
                    value="bankAccount" binary @click="store.setActivePaymentMethod('bankAccount')"
                  />
                  Credit Card
                  <!-- <span>(No processing fee)</span> -->
                  <div class="flex items-center gap-2 flex-1 justify-end">
                    <img src="@/assets/merchant/card-type/visa.png" alt="visa" class="w-8 w-8 border border-gray-200 rounded">
                    <img src="@/assets/merchant/card-type/mastercard.png" alt="mastercard" class="w-8 w-8 border border-gray-200 rounded">
                    <img src="@/assets/merchant/card-type/amex.png" alt="amex" class="w-8 w-8 border border-gray-200 rounded">
                  </div>
                </div>
                <transition @before-enter="beforeEnter" @enter="enter" @leave="leave">
                  <div v-show="store.activePaymentMethod === 'bankAccount'" class="payment-method-content">
                    <formPayment ref="formPaymentRef" />
                  </div>
                </transition>
                <div
                  class="payment-method-title" :class="[{ active: store.activePaymentMethod === 'card' }]"
                  :style="store.activePaymentMethod === 'card' ? store.config.headerStyle : {}"
                  @click="store.setActivePaymentMethod('card')"
                >
                  <RadioButton
                    class="mr-2" :model-value="store.activePaymentMethod === 'card'" value="card" binary
                    @click="store.setActivePaymentMethod('card')"
                  /> Bank Account
                  <!-- <span>(No processing fee)</span> -->
                </div>
                <transition @before-enter="beforeEnter" @enter="enter" @leave="leave">
                  <div v-show="store.activePaymentMethod === 'card'" class="payment-method-content">
                    <formBankAccountDetails ref="formBankAccountDetailsRef" />
                  </div>
                </transition>
              </div>

              <div class="flex justify-end gap-2">
                <!-- <Button label="Reset" severity="secondary" @click="resetForm" /> -->
                <Button label="CONTINUE" class="submit-button" @click="handleSubmit" />
              </div>
            </template>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
@use '@/styles/mixins/breakpoints' as *;

.checkout-page {
  min-height: 100vh;
  background: #f8f9fa;
  display: flex;
  justify-content: center;
  align-items: flex-start;

  .submit-button {
    min-width: 120px;
    margin: 24px 0;
  }

  .checkout-container {
    width: 100%;
    background: white;
    max-width: 700px;
    margin: 0 auto;

    .checkout-header {
      text-align: center;

      .merchant-logo {
        padding: 20px 0 24px 0;

        display: flex;
        justify-content: center;

        img {
          border-radius: 8px;
          object-fit: contain;
        }
      }
    }

    .currency-label {
      font-weight: 600;

      :deep(.p-select) {
        border: none;
        background-color: transparent;
        box-shadow: none;

        .p-select-label {
          padding: 0;
        }
      }
    }

    .checkout-content {
      padding: 0 2rem;

      @include media-breakpoint-down(md) {
        padding: 0 1rem;
      }
    }

    .payment-form {
      margin-top: 20px;
    }

    .header-content-merchant {
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: var(--p-primary-color);
      padding: 8px 12px;
      color: white;

      span,
      h2 {
        font-size: 22px;
      }

      h2 {
        font-weight: 600;
      }
    }

    .currency-selector {
      margin-top: 26px;
      background-color: #f7f7f7;
      border-radius: 8px;
      padding: 18px 26px;

      @include media-breakpoint-down(md) {
        padding: 12px 16px;
      }

      .currency-selector-description {
        font-size: 14px;
        color: var(--color-gray-600);
      }

      .currency-dropdown {

        :deep(.p-dropdown) {
          background: white;
          border-color: var(--p-primary-100);

          &:hover {
            border-color: var(--p-primary-color);
          }

          &:focus {
            box-shadow: 0 0 0 2px var(--p-primary-color);
            border-color: var(--p-primary-color);
          }
        }
      }
    }

    .card-title {
      font-size: 1.5rem;
      font-weight: bold;
      color: var(--colors-primary);
      margin-bottom: 20px;
    }

    .card-description {
      font-size: 14px;
      color: #64748b;
    }
  }

  label {
    font-weight: 600;
    color: var(--colors-primary);
  }

  :deep(.preview-content) {
    border: none;
    padding: 12px 0;
    background-color: rgb(247, 247, 247);

    @include media-breakpoint-down(md) {
      padding: 8px 0;
    }

    .preview-card-label {
      padding: 0.25rem 0.75rem;
      font-size: 14px;
    }

    .preview-card {
      margin-bottom: 0;
    }
  }

  .bank-card {
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 8px 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 12px;

    &:last-child {
      margin-bottom: 0;
    }

    &:hover {
      border-color: var(--p-primary-color);
    }

    &.active {
      border-color: var(--p-primary-color);
    }
  }

  .checkout-form {
    padding: 0 24rem;

    .field {
      margin-bottom: 1.5rem;

      label {
        display: block;
        margin-bottom: 0.5rem;
        color: #1e293b;
        font-weight: 500;
      }
    }

    :deep(.p-inputtext) {
      padding-left: 2.5rem;
    }

    :deep(.p-input-icon-left) {
      i {
        left: 0.75rem;
        color: #64748b;
      }
    }

    .form-footer {
      margin-top: 2rem;

      :deep(.p-button) {
        background: var(--p-primary-500);
        border: none;
        padding: 1rem;
        font-size: 1.125rem;
        font-weight: 600;

        &:hover {
          background: var(--p-primary-500);
        }

        &:focus {
          box-shadow: 0 0 0 2px #bfdbfe;
        }

        .p-button-icon {
          font-size: 1rem;
        }
      }

      .security-info {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 1rem;
        color: #64748b;
        font-size: 0.875rem;

        i {
          color: #10b981;
        }
      }
    }
  }

  .payment-method-wrap {
    margin-top: 1rem;
    border-radius: 8px;
    box-shadow: inset 0 0 0 1px #e2e8f0;
    border-top: 0;
    border-bottom: 0;

    .payment-method-title {
      font-size: 14px;
      font-weight: 600;
      color: var(--colors-primary);
      border-radius: 8px;
      padding: 12px 14px;
      font-weight: 600;
      cursor: pointer;

      span {
        font-weight: 400;
        color: #64748b;
      }

      &.active {
        border: 1px solid var(--p-primary-color);
      }
    }

    .payment-method-content {
      padding: 16px;
    }
  }

  /* State switching animation */
  .result-state {
    animation: fadeIn 0.3s ease-in-out;
  }

  .loading-state {
    animation: fadeIn 0.2s ease-in-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }

    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Skeleton animation */
  :deep(.p-skeleton) {
    animation: skeleton-animation 1.5s ease-in-out infinite;
    background: linear-gradient(90deg,
        rgba(226, 232, 240, 0.8) 25%,
        rgba(226, 232, 240, 0.6) 37%,
        rgba(226, 232, 240, 0.8) 63%);
    background-size: 400% 100%;
  }

  @keyframes skeleton-animation {
    0% {
      background-position: 100% 50%;
    }

    100% {
      background-position: 0 50%;
    }
  }
}
</style>
