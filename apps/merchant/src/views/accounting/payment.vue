<script setup lang="ts">
import { toTypedSchema } from '@vee-validate/zod'
import { Decimal } from 'decimal.js'
import { Field, Form as VeeForm } from 'vee-validate'
import { computed, reactive, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRoute } from 'vue-router'
import { z } from 'zod'
import GoogleRecaptcha from '@/components/googleRecaptchaV2/index.vue'
import { accounting as accountingApi } from '@/services/api'

const { t } = useI18n()
const route = useRoute()

// Get invoice ID from route params
const invoiceId = ref(route.params.id as string)

// Form reference for validation
const formRef = ref()

// 控制发票详情显示
const showInvoiceDetails = ref(false)

// 加载状态
const isLoading = ref(false)
const isFetchingInvoice = ref(false)

// 支付结果状态
const paymentResult = reactive({
  show: false,
  success: false,
  message: '',
  amount: '',
  date: '',
})

// 控制表单显示
const showForm = computed(() => !paymentResult.show)

// reCAPTCHA refs and state
const recaptchaRef = ref<InstanceType<typeof GoogleRecaptcha> | null>(null)
const recaptchaVerified = ref(false)

const maxAmount = ref(************)

// Form validation schema
const validationSchema = toTypedSchema(z.object({
  invoiceNumber: z.string().min(1, { message: 'Invoice number is required' }),
  amount: z.number({ invalid_type_error: 'Amount is required' })
    .min(0.01, { message: 'Amount must be greater than 0' })
    .superRefine((value, ctx) => {
      if (value > maxAmount.value) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: `Amount must be less than ${maxAmount.value}`,
        })
        return false
      }
      return true
    })
    .nullable(),
  email: z.string().email({ message: 'Please enter a valid email address' }),
  name: z.string().min(1, { message: 'Name is required' }),
  creditCardNumber: z.string()
    .min(13, { message: 'Card number must be between 13 and 19 digits' })
    .max(19, { message: 'Card number must be between 13 and 19 digits' })
    .refine((value) => {
      const cleanValue = value.replace(/\D/g, '')
      let sum = 0
      let shouldDouble = false
      for (let i = cleanValue.length - 1; i >= 0; i--) {
        let digit = Number.parseInt(cleanValue.charAt(i))
        if (shouldDouble) {
          digit *= 2
          if (digit > 9) { digit -= 9 }
        }
        sum += digit
        shouldDouble = !shouldDouble
      }
      return sum % 10 === 0
    }, 'Invalid card number'),
  expiryDate: z.string()
    .regex(/^\d{2}\/\d{2}$/, 'Expiry date must be in MM/YY format')
    .refine((value) => {
      const [month, year] = value.split('/')
      const currentDate = new Date()
      const currentYear = currentDate.getFullYear() % 100
      const currentMonth = currentDate.getMonth() + 1

      const expiryMonth = Number.parseInt(month)
      const expiryYear = Number.parseInt(year)

      if (expiryMonth < 1 || expiryMonth > 12) {
        return false
      }

      return !(expiryYear < currentYear || (expiryYear === currentYear && expiryMonth < currentMonth))
    }, 'Card has expired or invalid date'),
  securityCode: z.string()
    .regex(/^\d{3,4}$/, 'Security code must be 3 or 4 digits'),
  // Add recaptcha validation
  google_token: z.string({
    required_error: 'Please complete the reCAPTCHA verification',
  }),
}))

// Form initial values
const initialValues = reactive({
  invoiceNumber: '',
  amount: 0 as number | null,
  email: '',
  name: '',
  creditCardNumber: '',
  expiryDate: '',
  securityCode: '',
  recaptcha: false,
  google_token: '',
})

// Invoice details
const invoiceDetails = reactive({
  total: '0',
  amountPaid: '0',
  amountDue: '0',
  surcharge_rate: '0',
  // 1 : 百分比 2 : 固定金额
  surcharge_type: '0',
  currency: 'AUD',
})

const getActualPaymentAmount = () => {
  if (invoiceDetails.surcharge_type === '1') {
    return new Decimal(initialValues.amount || 0).mul(new Decimal(1).add(new Decimal(invoiceDetails.surcharge_rate || 0).div(100))).toFixed(2)
  }
  else {
    return new Decimal(initialValues.amount || 0).add(new Decimal(invoiceDetails.surcharge_rate || 0)).toFixed(2)
  }
}

// reCAPTCHA handlers
const onRecaptchaVerify = (response: string) => {
  if (response) {
    initialValues.google_token = response
    recaptchaVerified.value = true
  }
}

const onRecaptchaExpired = () => {
  recaptchaVerified.value = false
}

const onRecaptchaError = () => {
  recaptchaVerified.value = false
}

// Submit form
const onSubmit = async (values: Record<string, any>) => {
  // Check recaptcha verification
  if (!recaptchaVerified.value) {
    initialValues.recaptcha = false
  }

  if (!values.google_token) {
    return
  }

  isLoading.value = true

  try {
    const { data, code } = await accountingApi.pay({
      amount: Number(initialValues.amount),
      card_number: initialValues.creditCardNumber,
      email: initialValues.email,
      expiration_month: initialValues.expiryDate.split('/')[0],
      expiration_year: initialValues.expiryDate.split('/')[1],
      first_name: initialValues.name,
      last_name: initialValues.name,
      security_code: initialValues.securityCode,
      google_token: initialValues.google_token,
      invoice_number: initialValues.invoiceNumber,
      invoice_token: invoiceId.value,
    })

    if (code === 0) {
      // Payment successful
      paymentResult.success = true
      paymentResult.message = t('invoicePaymentPage.paymentSuccessMessage')
      paymentResult.amount = `$${initialValues.amount?.toFixed(2)}` || ''
      paymentResult.date = new Date().toLocaleString()
      paymentResult.show = true
    }
    else {
      // Payment failed with error code
      paymentResult.success = false
      paymentResult.message = data.message || t('invoicePaymentPage.paymentFailedMessage')
      paymentResult.show = true
      recaptchaRef.value?.reset()
    }
  }
  catch (error) {
    // Payment failed with exception
    paymentResult.success = false
    paymentResult.message = error instanceof Error ? error.message : t('invoicePaymentPage.paymentFailedMessage')
    paymentResult.show = true
    recaptchaRef.value?.reset()
  }
  finally {
    isLoading.value = false
  }
}

// Fetch invoice details
const fetchInvoice = async () => {
  if (initialValues.invoiceNumber) {
    // 显示加载状态
    isFetchingInvoice.value = true

    try {
      const { data, code } = await accountingApi.getInvoiceNumberDetail({
        invoice_token: invoiceId.value,
        invoice_number: initialValues.invoiceNumber,
      })

      if (code === 0) {
        invoiceDetails.total = data.total
        invoiceDetails.amountPaid = data.amount_paid
        invoiceDetails.amountDue = data.amount_due
        invoiceDetails.surcharge_rate = String(data.surcharge_rate?.fee_value)
        invoiceDetails.surcharge_type = String(data.surcharge_rate?.fee_rate)
        maxAmount.value = Number(data.amount_due)
        invoiceDetails.currency = data.currency
        showInvoiceDetails.value = true
      }
      else {
        invoiceDetails.total = '0'
        invoiceDetails.amountPaid = '0'
        invoiceDetails.amountDue = '0'
        invoiceDetails.surcharge_rate = '0'
        invoiceDetails.surcharge_type = '0'
        maxAmount.value = ************
        showInvoiceDetails.value = false
      }
    }
    finally {
      isFetchingInvoice.value = false
    }
  }
}

const reloadPage = () => {
  window.location.reload()
}
</script>

<template>
  <div class="payment-container">
    <!-- 主要内容区域 -->
    <div class="payment-content border-round overflow-hidden">
      <!-- 公司信息 -->
      <!-- <div class="company-info p-4 border-bottom-1 text-center">
        <h2 class="company-title">
          bill$buddy
        </h2>
        <p>Bill Buddy P/L</p>
        <p>ABN ***********</p>
        <div class="mt-3 ip-address">
          <p>
            <strong>Your IP Address:</strong> ************** (Any suspicious transactions are reported to the relevant
            authorities)
          </p>
        </div>
      </div> -->

      <!-- 发票详情 -->
      <div v-if="showInvoiceDetails" class="col-1 md:col-4 pl-md-4">
        <div class="invoice-details p-4 border-1 border-round h-full">
          <h3 class="invoice-summary-title mb-3">
            {{ t('invoicePaymentPage.invoiceSummary') }}
          </h3>
          <div class="flex">
            <div class="col-1">
              <div class="flex justify-content-between mb-2">
                <span>{{ t('invoicePaymentPage.invoiceTotal') }}</span>
                <span class="font-medium">{{ invoiceDetails.total }}</span>
              </div>
              <div class="flex justify-content-between mb-2">
                <span>{{ t('invoicePaymentPage.invoiceAmountPaid') }}</span>
                <span class="font-medium">{{ invoiceDetails.amountPaid }}</span>
              </div>
              <div class="flex justify-content-between mb-2">
                <span>{{ t('invoicePaymentPage.invoiceAmountDue') }}</span>
                <span class="font-medium">{{ invoiceDetails.amountDue }}</span>
              </div>
              <!-- 附加费 -->
              <div class="flex justify-content-between mb-2">
                <span class="mr-1">Surcharge: </span>
                <span v-if="invoiceDetails.surcharge_type === '1'" class="font-medium">
                  {{ invoiceDetails.surcharge_rate }}%
                </span>
                <span v-else class="font-medium">
                  {{ invoiceDetails.surcharge_rate }}
                </span>
              </div>
              <!-- 实际支付金额 -->
              <div class="flex justify-content-between mb-2">
                <span>Actual Payment Amount: </span>
                <span class="font-medium ml-2">
                  <span>{{ getActualPaymentAmount() }}</span>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 支付结果显示 -->
      <div v-if="paymentResult.show" class="payment-result p-4">
        <div class="result-container border-round p-4" :class="paymentResult.success ? 'success' : 'error'">
          <div class="result-icon text-center mb-4">
            <i :class="paymentResult.success ? 'pi pi-check-circle' : 'pi pi-times-circle'" style="font-size: 4rem" />
          </div>
          <h2 class="text-center mb-3">
            {{ paymentResult.success ? t('invoicePaymentPage.paymentSuccessTitle') : t('invoicePaymentPage.paymentFailedTitle') }}
          </h2>
          <p class="text-center mb-4">
            {{ paymentResult.message }}
          </p>

          <!-- 成功时显示交易详情 -->
          <div v-if="paymentResult.success" class="transaction-details border-1 border-round p-3 mb-4">
            <div class="grid">
              <div class="col-12">
                <div class="flex justify-content-between mb-2">
                  <span>{{ t('invoicePaymentPage.amountPaid') }}</span>
                  <span class="font-medium">{{ paymentResult.amount }}</span>
                </div>
                <div class="flex justify-content-between mb-2">
                  <span>{{ t('invoicePaymentPage.paymentDate') }}</span>
                  <span class="font-medium">{{ paymentResult.date }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="flex justify-content-center">
            <Button
              type="button"
              :label="paymentResult.success ? t('invoicePaymentPage.done') : t('invoicePaymentPage.tryAgain')"
              :icon="paymentResult.success ? 'pi pi-check' : 'pi pi-refresh'"
              :class="paymentResult.success ? 'p-button-success' : 'p-button-primary'"
              @click="paymentResult.success ? reloadPage() : paymentResult.show = false"
            />
          </div>
        </div>
      </div>

      <!-- 表单区域 -->
      <div v-if="showForm" class="payment-form p-4 relative">
        <ProgressSpinner v-if="isLoading" class="loading-spinner" stroke-width="4" />
        <VeeForm ref="formRef" :validation-schema="validationSchema" @submit="onSubmit">
          <div class="grid">
            <!-- 表单字段 -->
            <div class="col-1" :class="{ 'md:col-1': showInvoiceDetails }">
              <!-- Invoice Number -->
              <Field v-slot="{ field, errorMessage }" v-model="initialValues.invoiceNumber" name="invoiceNumber">
                <div class="field mb-4">
                  <label for="invoiceNumber" class="block mb-2">{{ t('invoicePaymentPage.invoiceNumber') }} *</label>
                  <div class="flex justify-content-between gap-4">
                    <InputText
                      id="invoiceNumber" v-bind="field" class="w-full"
                      placeholder="Please enter invoice number"
                    />
                    <Button
                      type="button" :label="t('invoicePaymentPage.fetchInvoice')" class="fetch-button"
                      :loading="isFetchingInvoice"
                      @click="fetchInvoice"
                    />
                  </div>
                  <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
                    {{ errorMessage }}
                  </Message>
                </div>
              </Field>

              <!-- Amount of Payment -->
              <Field v-slot="{ field, errorMessage, handleChange }" v-model="initialValues.amount" name="amount">
                <div class="field mb-4">
                  <label for="amount" class="block mb-2">{{ t('invoicePaymentPage.amountOfPayment') }} ({{ invoiceDetails.currency }})</label>
                  <InputNumber
                    id="amount" v-model="field.value" class="w-full"
                    :placeholder="t('invoicePaymentPage.atLeastTwoDecimalPlaces')" :min-fraction-digits="2"
                    :max-fraction-digits="2"
                    :max="************"
                    :disabled="Number(invoiceDetails.total) === 0 || Number(invoiceDetails.amountDue) === 0"
                    @value-change="handleChange"
                  />
                  <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
                    {{ errorMessage }}
                  </Message>
                </div>
              </Field>

              <!-- Email -->
              <Field v-slot="{ field, errorMessage }" v-model="initialValues.email" name="email">
                <div class="field mb-4">
                  <label for="email" class="block mb-2">{{ t('invoicePaymentPage.email') }}</label>
                  <InputText id="email" v-bind="field" :disabled="Number(invoiceDetails.total) === 0" class="w-full" placeholder="<EMAIL>" />
                  <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
                    {{ errorMessage }}
                  </Message>
                </div>
              </Field>

              <!-- Name -->
              <Field v-slot="{ field, errorMessage }" v-model="initialValues.name" name="name">
                <div class="field mb-4">
                  <label for="name" class="block mb-2">{{ t('invoicePaymentPage.name') }}</label>
                  <InputText id="name" v-bind="field" :disabled="Number(invoiceDetails.total) === 0" class="w-full" placeholder="Full Name" />
                  <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
                    {{ errorMessage }}
                  </Message>
                </div>
              </Field>

              <!-- Credit Card Number -->
              <Field v-slot="{ field, errorMessage }" v-model="initialValues.creditCardNumber" as="div" class="field mb-4" name="creditCardNumber">
                <label
                  for="creditCardNumber"
                  class="block mb-2"
                >{{ t('invoicePaymentPage.creditCardNumber') }}</label>
                <InputText id="creditCardNumber" v-bind="field" :disabled="Number(invoiceDetails.total) === 0" class="w-full" placeholder="XXXX XXXX XXXX XXXX" />
                <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
                  {{ errorMessage }}
                </Message>
              </Field>

              <!-- Expiry Date and securityCode in a row -->
              <Field v-slot="{ field, errorMessage }" v-model="initialValues.expiryDate" as="div" class="field mb-4" name="expiryDate">
                <div class="field mb-4">
                  <label for="expiryDate" class="block mb-2">{{ t('invoicePaymentPage.expireDate') }}</label>
                  <InputMask :model-value="field.value" :disabled="Number(invoiceDetails.total) === 0" placeholder="MM/YY" mask="99/99" class="w-full" />
                  <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
                    {{ errorMessage }}
                  </Message>
                </div>
              </Field>

              <Field v-slot="{ field, errorMessage }" v-model="initialValues.securityCode" as="div" class="field mb-4" name="securityCode">
                <label for="securityCode" class="block mb-2">{{ t('invoicePaymentPage.securityCode') }}</label>
                <InputText id="securityCode" v-bind="field" :disabled="Number(invoiceDetails.total) === 0" class="w-full" placeholder="123" />
                <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
                  {{ errorMessage }}
                </Message>
              </Field>

              <!-- Google reCAPTCHA -->
              <Field v-slot="{ errorMessage }" v-model="initialValues.google_token" name="google_token">
                <div class="field mb-4">
                  <label class="block mb-2">{{ t('invoicePaymentPage.verification') || 'Verification' }}</label>
                  <GoogleRecaptcha
                    ref="recaptchaRef"
                    class="mb-2"
                    @verify="onRecaptchaVerify"
                    @expired="onRecaptchaExpired"
                    @error="onRecaptchaError"
                  />
                  <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
                    {{ errorMessage }}
                  </Message>
                </div>
              </Field>
              <!-- Submit Button -->
              <div class="field">
                <Button
                  type="submit" :label="t('invoicePaymentPage.submit')"
                  class="w-full p-button-primary submit-button"
                />
              </div>
            </div>
          </div>
        </VeeForm>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.payment-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
}

.payment-content {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  background-color: #fff;
  border-radius: 10px;
  padding: 2rem;
}

.rules-section {
  font-size: 0.9rem;
  border-color: #e9ecef;
}

.rules-section ol {
  padding-left: 1.5rem;
  margin-bottom: 0;
}

.invoice-details {
  background-color: #f8f9fa;
  border-color: #e0e0e0 !important;
}

.fetch-button {
  min-width: 140px;
}

.loading-spinner {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 100;
}

:deep(.p-progress-spinner) {
  width: 50px;
  height: 50px;
}

/* Payment Result Styles */
.payment-result {
  max-width: 600px;
  margin: 0 auto;
}

.result-container {
  background-color: #fff;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);

  &.success {
    border-left: 5px solid #22c55e;

    .result-icon {
      color: #22c55e;
    }
  }

  &.error {
    border-left: 5px solid #ef4444;

    .result-icon {
      color: #ef4444;
    }
  }
}

.transaction-details {
  background-color: #f8f9fa;
  border-color: #e0e0e0 !important;
}

/* Responsive adjustments */
@media screen and (max-width: 768px) {
  .payment-container {
    padding: 1rem;
  }

  .pl-md-4 {
    padding-left: 0;
    margin-top: 2rem;
  }
}

/* Small mobile devices */
@media screen and (max-width: 480px) {
  :deep(.p-float-label) {
    font-size: 0.9rem;
  }

  :deep(.p-inputtext) {
    font-size: 0.9rem;
    padding: 0.5rem;
  }
}
</style>
