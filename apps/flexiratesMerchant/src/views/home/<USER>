<script setup lang="ts">
import { Format } from '@shared'
import dayjs from 'dayjs'
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>hart } from 'echarts/charts'
import { GraphicComponent, GridComponent, LegendComponent, TitleComponent, TooltipComponent } from 'echarts/components'
import * as echarts from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
// import Skeleton from 'primevue/skeleton'
import { nextTick, onMounted, ref } from 'vue'
// import Card<PERSON>hart from './components/cardChart.vue'
import { useRequestList } from '@/composables/useRequestList'
// import { useRouter } from 'vue-router'
// import EnhancedDatePicker from '@/components/common/EnhancedDatePicker.vue'
import { home as homeApi, transactions as transactionsApi } from '@/services/api'
import { useUserStore } from '@/store/modules/user'
import { formatDate } from '@/utils/date'
import { formatAmount } from '@/utils/format'

echarts.use([TitleComponent, Grid<PERSON><PERSON>ponent, <PERSON>vas<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Tooltip<PERSON>omponent, LegendComponent, GraphicComponent])

const { getUserInfo } = useUserStore()

const userName = ref('')

// const router = useRouter()
const isPageLoaded = ref(false)
const pageLoading = ref(true)
const Data = ref<any>()

const customerTable = ref()

const renderDom = ref()

const columns = ref<TableColumnItem[]>([
  { template: 'property_number', field: 'customer_plan.customer_property.property_number', header: 'Property Number', style: { minWidth: '120px' } },
  { template: 'remit_date', field: 'remit_date', header: 'Remit Date', style: { minWidth: '120px' } },
  { template: 'payment_amount', field: 'payment_amount', header: 'Amount', style: { minWidth: '200px' } },
  { template: 'payment_method', field: 'payment_method', header: 'PaymentMethod', style: { minWidth: '100px' } },
  { field: 'customer.name', header: 'Customer Name', style: { minWidth: '160px' } },
  { template: 'address', field: 'address', header: 'Address', style: { minWidth: '120px' } },
  { field: 'customer.email_primary', header: 'Email', style: { minWidth: '120px' } },
  { field: 'customer.phone_mobile', header: 'Mobile', style: { minWidth: '120px' } },
])

const {
  list,
  loading,
  total,
  failed,
  failureMessage,
} = useRequestList({
  requestFn: transactionsApi.getList,
  immediate: true,
  page_size: 10,
})

const renderChart = (data: Api.HomeChartData['weekly']) => {
  if (!renderDom.value) { return }
  const chart = echarts.init(renderDom.value as HTMLElement)
  const currentTime = dayjs()
  const lastTime = dayjs().subtract(1, 'y')

  const maxNumber = Math.max(...Object.values(data?.current).map(Number), ...Object.values(data?.last).map(Number))

  const chartOptions = ref(
    {
      title: {
        text: `Payments on ${lastTime.format('MMMM YYYY')} vs ${currentTime.format('MMMM YYYY')}`,
        textStyle: {
          color: '#545454',
          fontSize: '14px',
        },
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
      },
      grid: {
        left: '6%',
      },
      xAxis: {
        type: 'category',
        data: Object.keys(data?.current),
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          formatter: (value: any) => `$${value}`,
        },
        min: 0,
        max: Math.floor(maxNumber + maxNumber * 0.2),
      },
      color: ['#ebb700', '#0073cf'],
      legend: {
        data: [`FY-${lastTime.format('YYYY')}`, `FY-${currentTime.format('YYYY')}`],
        left: '0',
        top: '22px',
        icon: 'circle',
      },
      series: [
        {
          data: Object.values(data?.last),
          type: 'bar',
          tooltip: {
            valueFormatter(value: number) {
              return Format.formatAmount(value)
            },
          },
          itemStyle: {
            borderRadius: [3, 3, 0, 0],
          },
          name: `FY-${lastTime.format('YYYY')}`,
        },
        {
          data: Object.values(data?.current),
          type: 'bar',
          tooltip: {
            valueFormatter(value: number) {
              return `$${value}`
            },
          },
          itemStyle: {
            borderRadius: [3, 3, 0, 0],
          },
          name: `FY-${currentTime.format('YYYY')}`,
        },
      ],
    },
  )

  chart.setOption(chartOptions.value)
}

// 简单的页面加载动画
onMounted(() => {
  setTimeout(() => {
    isPageLoaded.value = true
  }, 300)
  Promise.all([
    getUserInfo().then((res) => {
      userName.value = res.name
    }),
    homeApi.getSumData().then((res) => {
      Data.value = res.data
      updateHomeData(res.data)
    }),
    homeApi.getMonthPayment(dayjs().format('YYYY-MM-DD')).then((res) => {
      updatePaymentsData(res.data)
    }),
    homeApi.getChartData({ date: dayjs().format('YYYY-MM') }).then(({ code, data }) => {
      if (code === 0) {
        nextTick(() => {
          renderChart(data?.weekly)
        })
      }
    }),
  ]).catch(() => {
    pageLoading.value = false
  })
})

const totalList = ref([
  {
    name: 'Total Active Registrations',
    amount: '00',
    // currency: 'AUD',
    detail: 'View all',
    // to: {
    //   name: 'transactionsList',
    //   query: {
    //     status: 3,
    //   },
    // },
  },
  {
    name: 'Total Active Email Registrations',
    amount: '0',
    // currency: 'AUD',
    detail: 'View all',
    // to: {
    //   name: 'transactionsList',
    //   query: {
    //     status: 1,
    //   },
    // },
  },
  {
    name: 'Total Active SMS Registrations',
    amount: '0',
    // currency: 'AUD',
    detail: 'View all',
    // to: {
    //   name: 'payoutList',
    //   query: {
    //     status: 0,
    //   },
    // },
  },
  {
    name: 'Total Properties',
    amount: '0',
    // currency: 'AUD',
    detail: 'View all',
    // to: {
    //   name: 'payoutList',
    //   query: {
    //     status: 0,
    //   },
    // },
  },
])
const nextList = ref([
  {
    name: 'Number of Pending Payments',
    amount: '0',
    // currency: 'AUD',
    detail: 'View all',
    // to: {
    //   name: 'transactionsList',
    //   query: {
    //     status: 1,
    //   },
    // },
  },
  {
    name: 'Amount of Pending Payments',
    amount: '0',
    // currency: 'AUD',
    detail: 'View all',
    // to: {
    //   name: 'payoutList',
    //   query: {
    //     status: 0,
    //   },
    // },
  },
])

const monthPaymentList = ref([
  {
    name: 'Total Payments Received',
    amount: '0',
    detail: 'View all',
    // to: {
    //   name: 'customersList',
    //   query: {
    //     category: 1,
    //   },
    // },
    helpText: 'Amount received from Ratepayers for current month.',
  },
  {
    name: 'Total Number of Failed Payments',
    amount: '0',
    detail: 'View all',
    // to: {
    //   name: 'customersList',
    //   query: {
    //     category: 5,
    //   },
    // },
    helpText: 'Count of payment failures in current month.',
  },
  {
    name: 'Total Payments Received via OTHER SOURCES',
    amount: '0',
    detail: 'View all',
    // to: {
    //   name: 'transactionsList',
    //   query: {
    //     status: 2,
    //   },
    // },
    helpText: 'Total amount received from Ratepayers via other sources like BPAY, Australia Post, Cash etc.',
  },
  {
    name: 'Total Amount in Arrears',
    amount: '0',
    detail: 'View all',
    // to: {
    //   name: 'transactionsList',
    //   query: {
    //     status: 2,
    //   },
    // },
    helpText: 'Total amount in Arrears from previous year.',
  },
])

const totalAmountMapping: Record<string, string> = {
  'Total Active Registrations': 'total_active_count',
  'Total Properties': 'total_property_count',
  'Total Active Email Registrations': '',
  'Total Active SMS Registrations': '',
}
const todayMapping: Record<string, string> = {
  'Number of Pending Payments': 'pending_payment_count',
  'Amount of Pending Payments': 'pending_payment_amount',
}
const monthPaymentMapping: Record<string, string> = {
  'Total Payments Received': 'total_payment_received',
  'Total Number of Failed Payments': 'total_failed_payments',
  'Total Payments Received via OTHER SOURCES': 'other_sources_total',
  'Total Amount in Arrears': 'arrears_amount_total',
}
const updateHomeData = (data: any) => {
  totalList.value.forEach((item) => {
    const key = totalAmountMapping[item.name]
    if (key) {
      item.amount = Format.formatNumber(data[key] || 0)
    }
  })

  nextList.value.forEach((item) => {
    const key = todayMapping[item.name]
    if (key) {
      item.amount = Format.formatNumber(data[key] || 0)
    }
  })
}

const updatePaymentsData = (data: any) => {
  monthPaymentList.value.forEach((item) => {
    const key = monthPaymentMapping[item.name]
    if (key) {
      item.amount = Format.formatNumber(data[key] || 0)
    }
  })
}
</script>

<template>
  <div class="home">
    <div class="merchant-common-page">
      <transition name="fade" appear>
        <div>
          <div class="welcome ">
            <div class="welcome-title">
              Welcome, {{ userName }}!
            </div>
          </div>
          <div class="w-full max-w-8xl">
            <div class="total">
              <div v-for="item in totalList" :key="item.name" class="total-item">
                <div class="statistical-title">
                  {{ item.name }}
                </div>
                <div class="total-content border-style">
                  <div class="total-amount">
                    {{ item.amount }}
                  </div>
                  <!-- <div class="total-link flex justify-between items-center">
                    <div class="cursor-pointer underline">
                      {{ item.detail }}
                    </div>
                    <i class="pi pi-chevron-right cursor-pointer" style="color: #545454;" />
                  </div> -->
                </div>
              </div>
            </div>
            <div class="next flex justify-between items-start gap-[4rem]">
              <div class="next-chart w-2/3">
                <div class="home-subtitle">
                  Month of {{ dayjs().format('MMMM') }}
                </div>
                <div id="chart" ref="renderDom" style="width: 100%;height: 500px;" />
              </div>
              <div class="next-sum w-1/3">
                <div v-for="item in nextList" :key="item.name" class="next-sum-item">
                  <div class="statistical-title">
                    {{ item.name }}
                  </div>
                  <div class="item-content border-style">
                    <div class="item-amount">
                      <span v-if="item.name === 'Amount of Pending Payments'">$</span>{{
                        item.amount }}
                    </div>
                    <!-- <div class="item-link flex justify-between items-center">
                      <div class="cursor-pointer underline">
                        {{ item.detail }}
                      </div>
                      <i class="pi pi-chevron-right cursor-pointer" style="color: #545454;" />
                    </div> -->
                  </div>
                </div>
              </div>
            </div>
            <div class="month">
              <div class="home-subtitle">
                This Month · {{ dayjs().format('MMMM') }}
              </div>
              <div class="month-content">
                <div v-for="item in monthPaymentList" :key="item.name" class="month-sum-item">
                  <div class="item-title flex items-center justify-between statistical-title">
                    <span>
                      {{ item.name }}
                    </span>
                    <BasePopover trigger="hover" placement="right" popper-class="user-setting-popper">
                      <template #reference>
                        <i class="pi pi-question-circle cursor-pointer" />
                      </template>
                      <div
                        class="max-w-lg break-normal p-2 text-left"
                      >
                        {{ item?.helpText }}
                      </div>
                    </BasePopover>
                  </div>
                  <div class="item-content border-style">
                    <div
                      class="item-amount"
                      :class="{ 'text-[#eb001b]': item.name === 'Total Number of Failed Payments' }"
                    >
                      <span v-if="item.name !== 'Total Number of Failed Payments'">$</span>{{
                        item.amount }}
                    </div>
                    <!-- <div class="item-link flex justify-between items-center">
                      <div class="cursor-pointer underline">
                        {{ item.detail }}
                      </div>
                      <i class="pi pi-chevron-right cursor-pointer" style="color: #545454;" />
                    </div> -->
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </transition>
    </div>
    <div class="merchant-common-page mt-6">
      <div class="payment-header flex justify-between items-center">
        <div class="home-subtitle">
          Payments List
        </div>
        <Button
          label="VIEW ALL PAYMENTS" severity="info"
          @click="$router.push({ name: 'flexiratesMerchantPayments' })"
        />
      </div>
      <BaseDataTable
        ref="customerTable" :show-search-bar="false" :value="list" :columns="columns" :scrollable="true"
        :show-multiple-column="false" :loading="loading" :paginator="false" :rows="6" :total-records="total"
        data-key="id" :failed="failed" :failure-message="failureMessage" :striped-rows="false"
        style="--frozen-column-border-bottom : -8px" :is-show-expander="false" :row-hover="true"
      >
        <template #property_number="{ data }">
          <span class="underline">
            {{ data?.customer_plan?.customer_property?.property_number }}
          </span>
        </template>
        <template #address="{ data }">
          <span v-if="data?.customer_plan?.customer_property?.street_address">
            {{ data?.customer_plan?.customer_property?.street_address }} ,
          </span>
          <span v-if="data?.customer_plan?.customer_property?.suburb">
            {{ data?.customer_plan?.customer_property?.suburb }}
          </span>
        </template>
        <template #payment_method="{ data }">
          <BaseCardType
            :card-type="data?.customer_banking?.credit_brand" :is-show-card-number="true"
            :text="data?.customer_banking?.account_no"
          />
        </template>
        <template #payment_amount="{ data }">
          <span>
            {{ formatAmount(data.payment_amount, data.payment_currency) }}
          </span>
        </template>
        <template #remit_date="{ data }">
          <span>
            {{ formatDate(data.created_at) }}
          </span>
        </template>
      </BaseDataTable>
    </div>
  </div>
</template>

<style scoped lang="scss">
@use '@/styles/mixins/breakpoints' as *;

/* 淡入动画 */
.fade-enter-active {
  transition: opacity 0.5s ease;
}

.fade-enter-from {
  opacity: 0;
}

/* 从下方淡入动画 */
.fade-up-enter-active {
  transition: all 0.5s ease;
  transition-delay: 0.2s;
}

.fade-up-enter-from {
  opacity: 0;
  transform: translateY(20px);
}

.welcome-title {
  font-size: 26px;
  font-weight: 900;
  color: #031f73;
}

.grey-color {
  color: #545454;
}

.border-style {
  border: 1px solid #949494;
}

.home-subtitle {
  color: #031f73;
  font-size: 20px;
  font-weight: 700;
}

.statistical-title {
  font-weight: 600;
  min-height: 33px;
  font-weight: 600;
  color: #545454;
}

.total {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  column-gap: 4rem;
  margin-top: 1.5rem;
  color: #545454;

  &-content {
    border-radius: 8px;
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
  }

  &-amount {
    font-size: 36px;
    font-weight: 800;
    // margin-bottom: 1.2rem;
  }

}

.next {
  margin-top: 3rem;

  .next-sum-item {
    margin-bottom: 1.5rem;
  }

  .item-content {
    color: #545454;
    border-radius: 8px;
    padding: 1.5rem;
    display: flex;
    flex-direction: column;

  }

  .item-amount {
    font-size: 36px;
    font-weight: 800;
    // margin-bottom: 1.2rem;
  }

}

.month {

  &-content {
    color: #545454;
    margin-top: 1.5rem;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    column-gap: 3rem;

    .item-content {
      border-radius: 8px;
      padding: 1.5rem;

    }

    .item-amount {
      font-size: 36px;
      font-weight: 800;
      // margin-bottom: 1.2rem;
    }

  }

}
</style>
