<script setup lang="ts">
// import dayjs from 'dayjs'
import { onMounted, ref } from 'vue'
import { useExport } from '@/composables/useExport'
import { security as securityApi } from '@/services/flexirates'
import ActivityLog from './components/activityLog.vue'

const devicesList = ref<any>([])
// const revokeLoading = ref(false)

// const columns = ref<TableColumnItem[]>([
//   {
//     field: 'device_name',
//     header: 'Devices Name',
//     style: { width: '150px' },
//   },
//   {
//     field: 'since',
//     header: 'Active Since',
//     style: { width: '120px' },
//     template: 'since',
//   },
//   {
//     field: 'activity',
//     header: 'Last Activity',
//     style: { width: '120px' },
//     template: 'activity',
//   },
//   {
//     field: 'location',
//     header: 'Location',
//     style: { width: '120px' },
//   },
//   {
//     field: 'revoke',
//     header: 'Revoke',
//     style: { width: '120px' },
//     template: 'revoke',
//   },
// ])

const currentParams = ref()

const { handleExport, isExporting } = useExport({
  exportFn: securityApi.exportActivityLog,
  getParams: () => ({
    activity_types: currentParams.value,
  }),
})

const loading = ref(false)
const getInfo = async () => {
  loading.value = true
  const res = await securityApi.getAuth()
  if (res.code === 0) {
    devicesList.value = res.data
  }
  loading.value = false
}

// const deviceRevoke = async (id: number) => {
//   revokeLoading.value = true
//   const res = await securityApi.revokeDevice({ id })
//   if (res.code === 0) {
//     revokeLoading.value = false
//     window.$toast.add({
//       severity: 'success',
//       summary: 'Success',
//       detail: 'Revoke successfully!',
//     })
//     getInfo()
//   }
// }
onMounted(() => {
  getInfo()
})
</script>

<template>
  <div class="flexirates-wrap">
    <div class="flexirates-title">
      <div class="flexirates-title-text">
        Security
      </div>
    </div>
    <div class="content">
      <div class="update-password">
        <div class="subtitle">
          Update Password
        </div>
        <div class="flex justify-between items-center pt-10">
          <div class="text-light-grey">
            Would you like to change your password?
          </div>
          <div>
            <Button
              label="UPDATE PASSWORD" severity="warn" class="btn"
              @click="$router.push({ name: 'securityUpdatePassword' })"
            />
          </div>
        </div>
      </div>
      <!-- <div class="authenticated">
        <div class="subtitle">
          Your Authenticated Devices
        </div>
        <div class="text-light-grey py-10">
          This list shows devices that have been authenticated against your account.can revoke a devices authentication
          token. If you wish to continue using Bdy on that device, you will be prompted to
          enter your authentication details again.
        </div>
        <div>
          <BaseDataTable
            :columns="columns" :value="devicesList" :paginator="false" :rows="20" :lazy="true"
            data-key="id" :show-search-bar="false" :show-multiple-column="false" :scrollable="true"
            table-style="min-width: 50rem" :loading="loading"
          >
            <template #since="{ data }">
              <span>
                {{ dayjs(data.active_since).format('DD MMMM YYYY') }}
              </span>
            </template>
            <template #activity="{ data }">
              <span>
                {{ data.last_activity ? dayjs(data.last_activity).format('DD MMMM YYYY hh:mm A') : '' }}
              </span>
            </template>
            <template #revoke="{ data }">
              <span>
                <Button
                  label="Revoke" class="btn" severity="warn" :loading="revokeLoading"
                  @click="deviceRevoke(data.id)"
                />
              </span>
            </template>
          </BaseDataTable>
        </div>
      </div> -->

      <div class="activity-log mt-4">
        <div class="flex justify-between items-center">
          <div class="title">
            <div class="subtitle">
              Activity Log
            </div>
            <p>
              Review recent activity on your account, including logins, security changes, and payment updates.
            </p>
          </div>
          <div>
            <Button label="EXPORT" class="w-40" :loading="isExporting" @click="handleExport('csv')" />
          </div>
        </div>
        <ActivityLog v-model:params="currentParams" />
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.subtitle {
  font-size: 1.5rem;
  font-weight: 600;
}

.btn {
  display: inline-block;
  padding: 10px 50px;
}

.text-light-grey {
  color: #7f7f7f;
}

.content {
  color: var(--color-gray-500);

  .update-password,
  .authenticated {
    padding: 3rem 0;
  }

  .update-password {
    border-bottom: 1px solid #7f7f7f;
  }
}

.activity-log {
  color: var(--color-gray-500);
}
</style>
