<script setup lang="ts">
import type { FormContext } from 'vee-validate'
import { Constants, Format } from '@shared'
import { toTypedSchema } from '@vee-validate/yup'
import dayjs from 'dayjs'
import { Decimal } from 'decimal.js'
import { storeToRefs } from 'pinia'
import { Field, Form as VeeForm } from 'vee-validate'
import { computed, nextTick, onMounted, reactive, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import * as yup from 'yup'
import formPaymentImage from '@/assets/flexirates/register-payment-method.png'
import logo from '@/assets/flexiratesMerchant/logo.png'
import TermsAndConditions from '@/components/termsAndConditions/index.vue'
import { useCountryStore } from '@/store/modules/country'
import { useFlexiratesRegisterStore } from '@/store/modules/register'
import { useUserStore } from '@/store/modules/user'
import BottomInfo from './components/bottomInfo.vue'

enum PaymentMethod {
  BANK_ACCOUNT = 1,
  CREDIT_CARD = 2,
}

const route = useRoute()

const router = useRouter()

const showTerms = ref(false)

const paymentPlanOptions = ref<{ label: string, value: number }[]>([
  {
    label: 'Calculated Installments',
    value: Constants.Flexirates.PaymentPlan.CALCULATED_INSTALLMENTS,
  },
  {
    label: 'Custom Installment Amount',
    value: Constants.Flexirates.PaymentPlan.CUSTOM_INSTALLMENT_AMOUNT,
  },
  {
    label: 'Pay Full Amount',
    value: Constants.Flexirates.PaymentPlan.PAY_FULL_AMOUNT_NOW,
  },
])

const dictPaymentPlan = ref<{ label: string, value: number }[]>([
  {
    label: 'Weekly',
    value: Constants.Flexirates.Plan.WEEKLY,
  },
  {
    label: 'Fortnightly',
    value: Constants.Flexirates.Plan.FORTNIGHTLY,
  },
  {
    label: 'Monthly',
    value: Constants.Flexirates.Plan.MONTHLY,
  },
  {
    label: 'Quarterly',
    value: Constants.Flexirates.Plan.QUARTERLY,
  },
])

const { registerAccountInfo, register: registerAction } = useUserStore()

const registerStore = useFlexiratesRegisterStore()

const { model, currentStep } = storeToRefs(registerStore)

const loadings = reactive({
  register: false,
  submit: false,
  getRegisterAccountInfo: false,
})

const lastPaymentAmount = ref<string>('')
const calculatedLastPaymentDate = ref<string>('')

const countryStore = useCountryStore()

const { countries: countryOptions, isLoading: isGetCountryLoading, isLoaded } = storeToRefs(countryStore)

const stateOptions = ref<Array<{ name: string, code: string }>>([])

const setCountry = (value: string, isInit = false) => {
  if (!isInit) {
    model.value.card.billingAddress.state = ''
  }
  const country = countryOptions.value.find((item: any) => item.iso2 === value)
  if (country && country.state && country.state.length > 0) {
    stateOptions.value = country?.state
  }
  else {
    stateOptions.value = []
  }
}

const paymentPlanRef = ref<FormContext>()
const paymentMethodRef = ref<FormContext>()

const paymentPlanSchema = toTypedSchema(yup.object({
  flexiRates: yup.number().min(0, 'Payment plan is required'),
  first_payment_date: yup.date()
    .min(dayjs().add(3, 'day').startOf('day').toDate(), 'First payment date must be 3 days from today or in the future')
    .max(dayjs(registerAccountInfo?.last_payment_date).toDate(), 'First payment date must not be after the last payment date'),
  amount: yup.number().min(0, 'Amount is required'),
}))

const paymentMethodSchema = toTypedSchema(yup.object({
  paymentType: yup.string().min(1, 'Payment method is required'),

  // Validate fields based on payment type
  // Bank account validations
  bsb: yup.string().optional().test('bsb-format', 'Please enter a valid BSB number (e.g. 123-456 or 123456)', (value) => {
    if (model.value.paymentMethod === PaymentMethod.BANK_ACCOUNT) {
      if (!value || value.trim() === '') {
        return false
      }
      return /^\d{3}-\d{3}$|^\d{6}$/.test(value)
    }
    return true
  }),

  account_no: yup.string().optional().test('account_no', 'Please enter a valid account number (6-10 digits)', (value) => {
    if (model.value.paymentMethod === PaymentMethod.BANK_ACCOUNT) {
      if (!value || value.trim() === '') {
        return false
      }
      return /^\d{6,10}$/.test(value)
    }
    return true
  }),

  account_name: yup.string().optional().test('account_name', 'Bank account name is required', (value) => {
    if (model.value.paymentMethod === PaymentMethod.BANK_ACCOUNT && (!value || value.trim() === '')) {
      return false
    }
    return true
  }),

  nickname: yup.string().optional(),

  // Credit card validations
  card_number: yup.string().optional().test('card-number', 'Please enter a valid 16-digit card number', (value) => {
    if (model.value.paymentMethod === PaymentMethod.CREDIT_CARD) {
      if (!value || value.trim() === '') {
        return false
      }
      return /^\d{16}$/.test(value.replace(/\s/g, ''))
    }
    return true
  }),

  name_on_card: yup.string().optional().test('name-on-card', 'Name on card is required', (value) => {
    if (model.value.paymentMethod === PaymentMethod.CREDIT_CARD && (!value || value.trim() === '')) {
      return false
    }
    return true
  }),

  expiry_date: yup.string().optional().test('expiry-date', 'Please enter a valid expiry date (format: MM/YY)', (value) => {
    if (model.value.paymentMethod === PaymentMethod.CREDIT_CARD) {
      if (!value || value.trim() === '') {
        return false
      }
      const [month, year] = value.split('/').map(Number)
      const expiry_date = new Date(2000 + year, month - 1)
      const currentDate = new Date()
      return expiry_date >= currentDate
    }
    return true
  }),

  security_code: yup.string().optional().test('security-code', 'Please enter a valid security code (3-4 digits)', (value) => {
    if (model.value.paymentMethod === PaymentMethod.CREDIT_CARD) {
      if (!value || value.trim() === '') {
        return false
      }
      return /^\d{3,4}$/.test(value)
    }
    return true
  }),

  // Credit card billing address validations
  country: yup.string().optional().test('country', 'Country is required', (value) => {
    if (model.value.paymentMethod === PaymentMethod.CREDIT_CARD && (!value || value.trim() === '')) {
      return false
    }
    return true
  }),

  billingFirstName: yup.string().optional().test('billing-first-name', 'First name is required', (value) => {
    if (model.value.paymentMethod === PaymentMethod.CREDIT_CARD && (!value || value.trim() === '')) {
      return false
    }
    return true
  }),

  billingLastName: yup.string().optional().test('billing-last-name', 'Last name is required', (value) => {
    if (model.value.paymentMethod === PaymentMethod.CREDIT_CARD && (!value || value.trim() === '')) {
      return false
    }
    return true
  }),

  billingCompany: yup.string().optional(),

  billingAddress: yup.string().optional().test('billing-address', 'Address is required', (value) => {
    if (model.value.paymentMethod === PaymentMethod.CREDIT_CARD && (!value || value.trim() === '')) {
      return false
    }
    return true
  }),

  billingAddressLine2: yup.string().optional(),

  billingCity: yup.string().optional().test('billing-city', 'City is required', (value) => {
    if (model.value.paymentMethod === PaymentMethod.CREDIT_CARD && (!value || value.trim() === '')) {
      return false
    }
    return true
  }),

  billingState: yup.string().optional().test('billing-state', 'State is required', (value) => {
    if (model.value.paymentMethod === PaymentMethod.CREDIT_CARD && (!value || value.trim() === '')) {
      return false
    }
    return true
  }),

  billingPostcode: yup.string().optional().test('billing-postcode', 'Postcode is required', (value) => {
    if (model.value.paymentMethod === PaymentMethod.CREDIT_CARD && (!value || value.trim() === '')) {
      return false
    }
    return true
  }),

  billingPhone: yup.string().optional().test('billing-phone', 'Phone is required', (value) => {
    if (model.value.paymentMethod === PaymentMethod.CREDIT_CARD && (!value || value.trim() === '')) {
      return false
    }
    return true
  }),

  billingEmail: yup.string().optional().test('billing-email', 'Please enter a valid email address', (value) => {
    if (model.value.paymentMethod === PaymentMethod.CREDIT_CARD) {
      if (!value || value.trim() === '') {
        return false
      }
      return /^[^\s@]+@[^\s@][^\s.@]*\.[^\s@]+$/.test(value)
    }
    return true
  }),
}))

const handleNextStep = async () => {
  if (currentStep.value === 1) {
    const result = await paymentPlanRef.value?.validate()
    if (result?.valid) {
      currentStep.value++
    }
  }
  window.scrollTo({
    top: 0,
    behavior: 'smooth',
  })
}

const handleSubmit = async () => {
  const result = await paymentMethodRef.value?.validate()
  console.log('result', result)

  if (!result?.valid) { return }
  if (result?.valid) {
    if (!model.value.terms) {
      window.$toast.add({
        severity: 'error',
        summary: 'Error',
        detail: 'You must agree to the terms and conditions',
      })
      return
    }
  }

  try {
    loadings.submit = true

    const submitData = {
      return_url: window.location.href,
      type: model.value.paymentMethod === PaymentMethod.BANK_ACCOUNT ? 1 : 2,
      weight: model.value.is_default_payment_method ? 1 : 2,
    } as Api.FlexiratesUserRegisterCreateBankingReq

    // 银行账户信息
    if (model.value.paymentMethod === PaymentMethod.BANK_ACCOUNT) {
      submitData.bank = {
        bsb: model.value.bank.bsb.replace(/-/g, ''),
        account_no: model.value.bank.account_no,
        account_name: model.value.bank.account_name,
        nickname: model.value.bank.nickname,
      }
    }
    // 信用卡信息
    else if (model.value.paymentMethod === PaymentMethod.CREDIT_CARD) {
      submitData.card = {
        first_name: model.value.card.billingAddress.first_name,
        last_name: model.value.card.billingAddress.last_name,
        card_number: model.value.card.card_number,
        security_code: model.value.card.security_code,
        name_on_card: model.value.card.name_on_card,
        expiration_year: model.value.card.expiry_date.split('/')[1],
        expiration_month: model.value.card.expiry_date.split('/')[0],
        email: model.value.card.email,
        country_iso2: model.value.card.billingAddress.country,
        postcode: model.value.card.billingAddress.postal_code,
        state: model.value.card.billingAddress.state,
        city: model.value.card.billingAddress.city,
        line_1: model.value.card.billingAddress.address_line1,
        line_2: model.value.card.billingAddress.address_line2,
        phone: model.value.card.billingAddress.phone,
      }
    }

    const { code, data } = await registerStore.createBanking(submitData)
    if (code === 0) {
      // 保存银行账户信息
      if (data.banking_id) {
        model.value.banking_id = data?.banking_id as number
        submitRegister(data.banking_id)
      }
      if (data.html) {
        const div = document.createElement('div')
        div.innerHTML = data.html
        document.body.appendChild(div)
        nextTick(() => {
          const stepUpForm = document.querySelector('#step-up-form') as HTMLFormElement
          if (stepUpForm) {
            stepUpForm?.submit()
          }
        })
      }
    }
  }
  finally {
    loadings.submit = false
  }
}

const submitRegister = async (banking_id: string | number) => {
  let amount = null
  let payment_plan_schedule = model.value.payment_plan_schedule

  if (model.value.payment_plan === Constants.Flexirates.PaymentPlan.PAY_FULL_AMOUNT_NOW) {
    amount = registerAccountInfo?.total_amount_due || 0
    payment_plan_schedule = Constants.Flexirates.Plan.FULL_AMOUNT
  }
  else if (model.value.payment_plan === Constants.Flexirates.PaymentPlan.CALCULATED_INSTALLMENTS) {
    amount = model.value.regular_payment_amount
  }
  else {
    amount = model.value.amount
  }

  try {
    loadings.submit = true
    const result = await registerAction({
      bank_id: Number(banking_id),
      first_name: model.value.first_name,
      last_name: model.value.last_name,
      email: model.value.email,
      mobile: model.value.mobile_phone,
      password: model.value.password,
      is_notice: model.value.notice ? 1 : 0,
      payment_frequency: {
        amount: Number(amount),
        first_payment_date: dayjs(model.value.first_payment_date).format('YYYY-MM-DD'),
        payment_plan: payment_plan_schedule,
      },
      property: {
        property_number: model.value.property_number,
        verification_code: model.value.verification_code,
      },
    })

    if (result) {
      window.$toast.add({
        severity: 'success',
        summary: 'Success',
        detail: 'Registration successful',
      })
      registerStore.reset()
      router.replace('/home')
    }
    else {
      registerStore.setCurrentStep(2)
    }
  }
  finally {
    loadings.submit = false
  }
}

const handleBackStep = () => {
  currentStep.value--
}

const calculateDynamicLastPaymentDate = (firstPaymentDate: Date, paymentSchedule: number, numberOfPayments: number) => {
  if (!firstPaymentDate || !paymentSchedule || numberOfPayments <= 0) {
    return ''
  }

  const firstDate = dayjs(firstPaymentDate)
  let lastDate = firstDate

  // Calculate the last payment date based on payment frequency and number of payments
  switch (paymentSchedule) {
    case Constants.Flexirates.Plan.WEEKLY:
      lastDate = firstDate.add((numberOfPayments - 1) * 7, 'day')
      break
    case Constants.Flexirates.Plan.FORTNIGHTLY:
      lastDate = firstDate.add((numberOfPayments - 1) * 14, 'day')
      break
    case Constants.Flexirates.Plan.MONTHLY:
      lastDate = firstDate.add(numberOfPayments - 1, 'month')
      break
    case Constants.Flexirates.Plan.QUARTERLY:
      lastDate = firstDate.add((numberOfPayments - 1) * 3, 'month')
      break
    default:
      lastDate = firstDate
      break
  }

  return lastDate.format('YYYY-MM-DD')
}

const calculateCustomInstallmentPayments = (customAmount: string) => {
  if (!model.value.first_payment_date || !model.value.payment_plan_schedule || !customAmount || Decimal(customAmount).lte(0)) {
    model.value.no_of_regular_payment = 0
    lastPaymentAmount.value = '0'
    calculatedLastPaymentDate.value = ''
    return
  }

  const totalAmountDue = Decimal(registerAccountInfo?.total_amount_due || 0)
  const amount = Decimal(customAmount)

  const firstPaymentDateDate = dayjs(model.value.first_payment_date)

  // Calculate the maximum possible payments within the 2025-2026 period
  const endOfPeriod = dayjs('2026-06-30') // End of 2025-2026 financial year
  let maxPossiblePayments = 0

  // Calculate maximum possible payments based on payment frequency and date range
  switch (model.value.payment_plan_schedule) {
    case Constants.Flexirates.Plan.QUARTERLY:
      maxPossiblePayments = Math.abs(endOfPeriod.diff(firstPaymentDateDate, 'quarter')) + 1
      break
    case Constants.Flexirates.Plan.MONTHLY:
      maxPossiblePayments = Math.abs(endOfPeriod.diff(firstPaymentDateDate, 'month')) + 1
      break
    case Constants.Flexirates.Plan.FORTNIGHTLY: {
      const daysDiff = endOfPeriod.diff(firstPaymentDateDate, 'day') + 1
      maxPossiblePayments = daysDiff <= 14 ? 1 : Math.ceil(daysDiff / 14)
      break
    }
    case Constants.Flexirates.Plan.WEEKLY: {
      const daysDiff = endOfPeriod.diff(firstPaymentDateDate, 'day') + 1
      maxPossiblePayments = daysDiff <= 7 ? 1 : Math.ceil(daysDiff / 7)
      break
    }
    default:
      maxPossiblePayments = 1
      break
  }

  // Calculate how many payments are needed with the custom amount using Decimal
  const regularPaymentsNeeded = totalAmountDue.div(amount).floor().toNumber()

  // Use the minimum of calculated payments needed and maximum possible payments
  const actualRegularPayments = Math.min(regularPaymentsNeeded, maxPossiblePayments)

  // Calculate the last payment amount using Decimal
  const remainingAmount = totalAmountDue.sub(amount.mul(actualRegularPayments)).toFixed(2, Decimal.ROUND_HALF_UP)

  model.value.no_of_regular_payment = actualRegularPayments
  lastPaymentAmount.value = remainingAmount

  // If there's a remaining amount and we haven't reached the maximum payments, add one more payment
  if (Decimal(remainingAmount).gt(0) && actualRegularPayments < maxPossiblePayments) {
    model.value.no_of_regular_payment = actualRegularPayments + 1
  }

  // Calculate and update the dynamic last payment date
  calculatedLastPaymentDate.value = calculateDynamicLastPaymentDate(
    model.value.first_payment_date,
    model.value.payment_plan_schedule,
    model.value.no_of_regular_payment,
  )
}

const calculateRegularPaymentAmount = () => {
  if (!model.value.first_payment_date || !model.value.payment_plan_schedule || !registerAccountInfo?.last_payment_date) {
    model.value.no_of_regular_payment = 0
    model.value.amount = '0'
    return
  }

  // Check if this is for custom installment amount mode
  if (paymentPlanRef?.value && paymentPlanRef?.value?.values?.payment_plan === Constants.Flexirates.PaymentPlan.CUSTOM_INSTALLMENT_AMOUNT) {
    // For custom installment amount, use the custom calculation
    if (paymentPlanRef?.value?.values?.amount) {
      calculateCustomInstallmentPayments(paymentPlanRef?.value?.values?.amount as string)
    }
    return
  }

  // Original logic for calculated installments mode
  const totalAmountDue = registerAccountInfo?.total_amount_due || 1

  const firstPaymentDateDate = dayjs(model.value.first_payment_date)
  const lastPaymentDateDate = dayjs(registerAccountInfo?.last_payment_date)

  let paymentCount = 0
  let amount = '0'

  switch (model.value.payment_plan_schedule) {
    // 一次性付款
    case Constants.Flexirates.Plan.FULL_AMOUNT:
      paymentCount = 1
      amount = Decimal(registerAccountInfo?.total_amount_due || 1).toFixed(2, Decimal.ROUND_HALF_UP)
      break
    // 季度付款
    case Constants.Flexirates.Plan.QUARTERLY:
      paymentCount = Math.abs(lastPaymentDateDate.diff(firstPaymentDateDate, 'quarter') || 1)
      amount = Decimal(totalAmountDue).div(paymentCount).toFixed(2, Decimal.ROUND_HALF_UP)
      break
    // 月度付款
    case Constants.Flexirates.Plan.MONTHLY:
      paymentCount = Math.abs(lastPaymentDateDate.diff(firstPaymentDateDate, 'month') || 1)
      amount = Decimal(totalAmountDue).div(paymentCount).toFixed(2, Decimal.ROUND_HALF_UP)
      break
    // 双周付款
    case Constants.Flexirates.Plan.FORTNIGHTLY: {
      const daysDiff = lastPaymentDateDate.diff(firstPaymentDateDate, 'day') + 1
      paymentCount = daysDiff <= 14 ? 1 : Math.ceil(daysDiff / 14)
      amount = Decimal(totalAmountDue).div(paymentCount).toFixed(2, Decimal.ROUND_HALF_UP)
      break
    }
    // 周度付款
    case Constants.Flexirates.Plan.WEEKLY: {
      const daysDiff = lastPaymentDateDate.diff(firstPaymentDateDate, 'day') + 1
      paymentCount = daysDiff <= 7 ? 1 : Math.ceil(daysDiff / 7)
      amount = Decimal(totalAmountDue).div(paymentCount).toFixed(2, Decimal.ROUND_HALF_UP)
      break
    }
  }

  model.value.regular_payment_amount = amount
  model.value.no_of_regular_payment = Math.floor(paymentCount)
}

const calculateEndYear = (firstPaymentDate: Date, paymentSchedule: number, numberOfPayments: number): number => {
  if (!firstPaymentDate || !paymentSchedule || numberOfPayments <= 0) {
    return dayjs().year()
  }

  const firstDate = dayjs(firstPaymentDate)
  let endDate = firstDate

  switch (paymentSchedule) {
    case Constants.Flexirates.Plan.WEEKLY:
      endDate = firstDate.add((numberOfPayments - 1) * 7, 'day')
      break
    case Constants.Flexirates.Plan.FORTNIGHTLY:
      endDate = firstDate.add((numberOfPayments - 1) * 14, 'day')
      break
    case Constants.Flexirates.Plan.MONTHLY:
      endDate = firstDate.add(numberOfPayments - 1, 'month')
      break
    case Constants.Flexirates.Plan.QUARTERLY:
      endDate = firstDate.add((numberOfPayments - 1) * 3, 'month')
      break
    default:
      endDate = firstDate
      break
  }

  return endDate.year()
}

const maxDate = computed(() => {
  const now = dayjs()
  const deadline = dayjs('2025-09-27')

  if (now.isAfter(deadline)) {
    // 如果当前时间已经超过 2025-09-27，返回当前时间加 30 天
    return now.add(30, 'day').toDate()
  }
  else {
    // 否则返回固定的 2025-09-30
    return dayjs('2025-09-30').toDate()
  }
})

watch([() => model.value.amount, () => model.value.first_payment_date, () => model.value.payment_plan_schedule, () => model.value.payment_plan, () => registerAccountInfo?.last_payment_date], () => {
  calculateRegularPaymentAmount()
}, { immediate: true })

watch(() => isLoaded, (newVal) => {
  if (newVal && model.value.card.billingAddress.country) {
    setCountry(model.value.card.billingAddress.country, true)
  }
}, {
  immediate: true,
})

onMounted(() => {
  if (model.value.payment_plan_schedule === 0) {
    model.value.payment_plan_schedule = registerAccountInfo?.default_payment_plan as number
    paymentPlanRef?.value?.setFieldValue('payment_plan_schedule', model.value.payment_plan_schedule)
  }

  if (route.query.status === '0' && route.query.banking_id) {
    registerStore.setCurrentStep(3)
    submitRegister(Number(route.query.banking_id))
  }
})
</script>

<template>
  <div class="register-page">
    <div class="register-form" :class="{ 'show-payment-method': currentStep === 2 || currentStep === 3 }">
      <div class="register-form-content">
        <div class="flex justify-center">
          <Image :src="logo" width="250px" alt="Image" />
        </div>
        <template v-if="currentStep === 1 || currentStep === 2">
          <div class="flex flex-row gap-x-6">
            <div class="mt-10 flex-1">
              <div class="payment-frequency">
                <h2 class="text-2xl font-bold mb-4">
                  Payment frequency
                </h2>
                <div class="payment-frequency-content flex flex-col gap-y-2">
                  <p><strong>Address:</strong> {{ registerAccountInfo?.address }} {{ registerAccountInfo?.suburb }}</p>
                  <p class="mt-1">
                    <strong>Total Amount Due:</strong>
                    {{ Format.formatAmount(new Decimal(registerAccountInfo?.total_amount_due
                      || 0).toFixed(2)) }}
                  </p>
                </div>
              </div>

              <VeeForm
                ref="paymentPlanRef" v-slot="{ values }" :validation-schema="paymentPlanSchema"
                class="flex flex-col gap-x-8 gap-y-4 my-6"
              >
                <Field
                  v-slot="{ errorMessage, field, handleChange }" v-model="model.payment_plan" name="payment_plan"
                  as="div" class="form-item"
                >
                  <label class="form-item__label is-required">Payment Plan</label>
                  <div class="form-item__content flex flex-col gap-y-2">
                    <template v-if="currentStep === 1">
                      <Select
                        v-model="field.value" :options="paymentPlanOptions" option-label="label"
                        option-value="value" placeholder="Select a Payment Plan" class="w-full"
                        @value-change="handleChange"
                      />
                      <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
                        {{ errorMessage }}
                      </Message>
                    </template>
                    <template v-else>
                      <span class="ml-2">
                        {{ paymentPlanOptions.find(option => option?.value === model.payment_plan as unknown as number)?.label }}
                      </span>
                    </template>
                  </div>
                </Field>
                <Field
                  v-if="[Constants.Flexirates.PaymentPlan.CALCULATED_INSTALLMENTS, Constants.Flexirates.PaymentPlan.CUSTOM_INSTALLMENT_AMOUNT].includes(values?.payment_plan as number)"
                  v-slot="{ errorMessage, field, handleChange }" v-model="model.payment_plan_schedule"
                  name="payment_plan_schedule" as="div" class="form-item"
                >
                  <label class="form-item__label is-required">Payment Schedule</label>
                  <div class="form-item__content flex flex-col gap-y-2">
                    <template v-if="currentStep === 1">
                      <Select
                        v-model="field.value" :options="dictPaymentPlan" option-label="label" option-value="value"
                        placeholder="Select a Payment Schedule" class="w-full" @value-change="handleChange"
                      />
                      <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
                        {{ errorMessage }}
                      </Message>
                    </template>
                    <template v-else>
                      <span class="ml-2">
                        {{ dictPaymentPlan.find(option => option?.value === model.payment_plan_schedule as unknown as number)?.label }}
                      </span>
                    </template>
                  </div>
                </Field>
                <Field
                  v-slot="{ errorMessage, field, handleChange }" v-model="model.first_payment_date"
                  name="first_payment_date" as="div" class="form-item"
                >
                  <label class="form-item__label is-required">First payment date</label>
                  <div class="form-item__content flex flex-col gap-y-2">
                    <template v-if="currentStep === 1">
                      <!-- :max-date="dayjs(model.last_payment_date).toDate()" -->
                      <DatePicker
                        v-model="field.value" :min-date="dayjs().add(3, 'day').toDate()"
                        :max-date="maxDate" date-format="dd/mm/yy" show-icon
                        class="w-full" :class="{ 'p-invalid': errorMessage }" @value-change="handleChange"
                      />
                      <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
                        {{ errorMessage }}
                      </Message>
                    </template>
                    <template v-else>
                      <span class="ml-2">
                        {{ dayjs(model.first_payment_date).format('DD/MM/YYYY') }}
                      </span>
                    </template>
                  </div>
                </Field>
                <Field
                  v-if="values?.payment_plan === Constants.Flexirates.PaymentPlan.CUSTOM_INSTALLMENT_AMOUNT"
                  v-slot="{ errorMessage, field, handleChange }" v-model="model.amount" name="amount" as="div"
                  class="form-item"
                >
                  <label class="form-item__label is-required">Amount</label>
                  <div class="form-item__content flex flex-col gap-y-2">
                    <template v-if="currentStep === 1">
                      <InputNumber
                        v-model="field.value" :min-fraction-digits="2" :max-fraction-digits="2"
                        :max="Number(registerAccountInfo?.total_amount_due || 0)" class="w-full"
                        @value-change="handleChange"
                      />
                      <div v-if="values.amount > 0" class="text-sm text-gray-600 mt-2">
                        <p v-if="Number(lastPaymentAmount) > 0">
                          The last payment amount will be: {{ Format.formatAmount(lastPaymentAmount) }}
                        </p>
                        <p v-else-if="Number(lastPaymentAmount) === 0">
                          All payments will be exactly {{ Format.formatAmount(new Decimal(values.amount).toFixed(2)) }}
                        </p>
                      </div>
                      <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
                        {{ errorMessage }}
                      </Message>
                    </template>
                    <template v-else>
                      <span class="ml-2">
                        {{ model.amount ? Format.formatAmount(new Decimal(model.amount).toFixed(2)) : '' }}
                      </span>
                      <div v-if="values.amount > 0" class="text-sm text-gray-600 ml-2 mt-1">
                        <p v-if="Number(lastPaymentAmount) > 0">
                          The last payment amount will be: {{ Format.formatAmount(lastPaymentAmount) }}
                        </p>
                        <p v-else-if="Number(lastPaymentAmount) === 0">
                          All payments will be exactly {{ Format.formatAmount(new Decimal(values.amount).toFixed(2)) }}
                        </p>
                      </div>
                    </template>
                  </div>
                </Field>
                <div class="form-item">
                  <label class="form-item__label">Last payment date :</label>
                  <div class="form-item__content">
                    <span class="ml-2">
                      <template
                        v-if="values?.payment_plan === Constants.Flexirates.PaymentPlan.CUSTOM_INSTALLMENT_AMOUNT && calculatedLastPaymentDate"
                      >
                        {{ dayjs(calculatedLastPaymentDate).format('DD/MM/YYYY') }}
                      </template>
                      <template v-else>
                        {{ registerAccountInfo?.last_payment_date ? dayjs(registerAccountInfo?.last_payment_date).format('DD/MM/YYYY') : '' }}
                      </template>
                    </span>
                  </div>
                </div>
                <div
                  v-if="values?.payment_plan === Constants.Flexirates.PaymentPlan.CALCULATED_INSTALLMENTS"
                  class="form-item"
                >
                  <label class="form-item__label">Regular payment amount :</label>
                  <div class="form-item__content">
                    <span class="ml-2">
                      {{ model.regular_payment_amount ? Format.formatAmount(new Decimal(model.regular_payment_amount).toFixed(2)) : '' }}
                    </span>
                  </div>
                </div>
                <div class="form-item">
                  <label class="form-item__label">Number of payments during
                    {{ dayjs().year() }}
                    <template
                      v-if="
                        values?.payment_plan === Constants.Flexirates.PaymentPlan.CUSTOM_INSTALLMENT_AMOUNT
                          && values?.first_payment_date
                          && values?.payment_plan_schedule
                          && model?.no_of_regular_payment
                      "
                    >
                      -
                      {{ calculateEndYear(new Date(values?.first_payment_date), values?.payment_plan_schedule, model?.no_of_regular_payment) }}
                    </template>
                    <template
                      v-else-if="
                        values?.payment_plan === Constants.Flexirates.PaymentPlan.CALCULATED_INSTALLMENTS
                          && values?.first_payment_date
                          && values?.payment_plan_schedule
                          && model?.no_of_regular_payment
                      "
                    >
                      -
                      {{ calculateEndYear(new Date(values?.first_payment_date), values?.payment_plan_schedule, model?.no_of_regular_payment) }}
                    </template>
                    <template v-else-if="registerAccountInfo?.last_payment_date">
                      - {{ dayjs(registerAccountInfo.last_payment_date).year() }}
                    </template>
                    :</label>
                  <span class="ml-2">
                    <template v-if="values?.payment_plan === Constants.Flexirates.PaymentPlan.PAY_FULL_AMOUNT_NOW">
                      1
                    </template>
                    <template v-else>
                      {{ model.no_of_regular_payment ? model.no_of_regular_payment : '' }}
                    </template>
                  </span>
                </div>
              </VeeForm>

              <p class="text-red-600 text-sm">
                Please Note that if your FlexiRates account accrues 3 dishonoured (declined) payments during the
                financial
                year, your FlexiRates registration will be cancelled.
              </p>
              <p class="text-red-600 text-sm">
                The regular payment amount may differ from the original setup amount if a payment is skipped or fails.
              </p>
            </div>

            <div v-if="currentStep === 2" class="flex-1 pl-8">
              <div class="text-2xl font-bold mb-6" style="margin-block: 24px 8px;color: #545454">
                Payment Method
              </div>

              <VeeForm
                ref="paymentMethodRef" :validation-schema="paymentMethodSchema"
                class="flex flex-col gap-x-8 gap-y-4 my-6"
              >
                <Field
                  v-slot="{ errorMessage, field, handleChange }" v-model="model.paymentMethod" name="paymentType"
                  as="div" class="form-item lg:mb-8"
                >
                  <label class="form-item__label" />
                  <div class="form-item__content flex flex-col gap-y-2">
                    <div class="flex flex-wrap gap-4">
                      <div class="flex items-center gap-2">
                        <RadioButton
                          v-model="field.value" input-id="card" name="pizza"
                          :value="PaymentMethod.CREDIT_CARD" @value-change="(e) => {
                            handleChange(e)
                            model.paymentMethod = e
                          }"
                        />
                        <label for="card" class="cursor-pointer">Card Number</label>
                      </div>
                      <div class="flex items-center gap-2">
                        <RadioButton
                          v-model="field.value" input-id="bankAccount" name="pizza"
                          :value="PaymentMethod.BANK_ACCOUNT" @value-change="(e) => {
                            handleChange(e)
                            model.paymentMethod = e
                          }"
                        />
                        <label for="bankAccount" class="cursor-pointer">Bank Account</label>
                      </div>
                    </div>
                    <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
                      {{ errorMessage }}
                    </Message>
                  </div>
                </Field>
                <template v-if="model.paymentMethod === PaymentMethod.BANK_ACCOUNT">
                  <Field
                    v-slot="{ field, errorMessage, handleChange }" v-model="model.bank.bsb" name="bsb" as="div"
                    class="form-item is-required"
                  >
                    <label class="form-item__label is-required">BSB Number</label>
                    <div class="form-item__content flex flex-col gap-y-2">
                      <InputText v-model="field.value" class="w-full" @value-change="handleChange" />
                      <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
                        {{ errorMessage }}
                      </Message>
                    </div>
                  </Field>
                  <Field
                    v-slot="{ field, errorMessage, handleChange }" v-model="model.bank.account_name"
                    name="account_name" as="div" class="form-item is-required"
                  >
                    <label class="form-item__label is-required">Account Name</label>
                    <div class="form-item__content flex flex-col gap-y-2">
                      <InputText v-model="field.value" class="w-full" @value-change="handleChange" />
                      <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
                        {{ errorMessage }}
                      </Message>
                    </div>
                  </Field>
                  <Field
                    v-slot="{ field, errorMessage, handleChange }" v-model="model.bank.account_no"
                    name="account_no" as="div" class="form-item is-required"
                  >
                    <label class="form-item__label is-required">Account Number</label>
                    <div class="form-item__content flex flex-col gap-y-2">
                      <InputText v-model="field.value" class="w-full" @value-change="handleChange" />
                      <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
                        {{ errorMessage }}
                      </Message>
                    </div>
                  </Field>
                  <Field
                    v-slot="{ field, errorMessage, handleChange }" v-model="model.bank.nickname" name="nickname"
                    as="div" class="form-item is-required"
                  >
                    <label class="form-item__label">Nickname</label>
                    <div class="form-item__content flex flex-col gap-y-2">
                      <InputText v-model="field.value" class="w-full" @value-change="handleChange" />
                      <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
                        {{ errorMessage }}
                      </Message>
                    </div>
                  </Field>
                </template>
                <template v-if="model.paymentMethod === PaymentMethod.CREDIT_CARD">
                  <Field
                    v-slot="{ errorMessage, field, handleChange }" v-model="model.card.card_number"
                    name="card_number" as="div" class="form-item"
                  >
                    <label class="form-item__label is-required">Card Number</label>
                    <div class="form-item__content flex flex-col gap-y-2">
                      <InputText
                        v-model="field.value" class="w-full" :class="{ 'p-invalid': errorMessage }"
                        @value-change="handleChange"
                      />
                      <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
                        {{ errorMessage }}
                      </Message>
                    </div>
                  </Field>

                  <div class="form-item mb-4">
                    <label class="form-item__label" />
                    <div class="form-item__content flex items-center">
                      <Image :src="formPaymentImage" width="100px" alt="Image" />
                    </div>
                  </div>

                  <Field
                    v-slot="{ field, errorMessage, handleChange }" v-model="model.card.expiry_date"
                    name="expiry_date" as="div" class="form-item is-required"
                  >
                    <label class="form-item__label is-required">Expiry Date</label>
                    <div class="form-item__content flex flex-col gap-y-2">
                      <InputMask
                        :model-value="field.value" placeholder="MM/YY" mask="99/99" class="w-full"
                        :class="{ 'p-invalid': errorMessage }" @value-change="handleChange"
                      />
                      <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
                        {{ errorMessage }}
                      </Message>
                    </div>
                  </Field>

                  <Field
                    v-slot="{ errorMessage, field, handleChange }" v-model="model.card.name_on_card"
                    name="name_on_card" as="div" class="form-item is-required"
                  >
                    <label class="form-item__label is-required">Name on Card</label>
                    <div class="form-item__content flex flex-col gap-y-2">
                      <InputText
                        v-model="field.value" class="w-full" :class="{ 'p-invalid': errorMessage }"
                        @value-change="handleChange"
                      />
                      <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
                        {{ errorMessage }}
                      </Message>
                    </div>
                  </Field>
                  <Field
                    v-slot="{ errorMessage, field, handleChange }" v-model="model.card.security_code"
                    name="security_code" as="div" class="form-item is-required"
                  >
                    <label class="form-item__label is-required">CVV</label>
                    <div class="form-item__content flex flex-col gap-y-2">
                      <InputText
                        v-model="field.value" class="w-full" :class="{ 'p-invalid': errorMessage }"
                        @value-change="handleChange"
                      />
                      <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
                        {{ errorMessage }}
                      </Message>
                    </div>
                  </Field>

                  <!-- 账单地址表单部分 -->
                  <div class="billing-address-form mt-6">
                    <h3 class="text-xl font-semibold mb-4">
                      Billing address
                    </h3>
                    <p class="text-gray-600 mb-4">
                      Select the address that matches your card or payment method.
                    </p>

                    <!-- Country/Region -->
                    <div class="field mb-4">
                      <Field
                        v-slot="{ field, errorMessage, handleChange }" v-model="model.card.billingAddress.country"
                        name="country"
                      >
                        <Select
                          id="country" v-model="field.value" class="w-full" :options="countryOptions"
                          option-label="name" option-value="iso2" placeholder="Select country"
                          :loading="isGetCountryLoading" filter show-clear @value-change="e => {
                            handleChange(e)
                            setCountry(e)
                          }"
                        />
                        <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
                          {{ errorMessage }}
                        </Message>
                      </Field>
                    </div>

                    <!-- Name fields (first name and last name) -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                      <div class="field">
                        <Field
                          v-slot="{ field, errorMessage }" v-model="model.card.billingAddress.first_name"
                          name="billingFirstName"
                        >
                          <InputText
                            v-bind="field" id="firstName" class="w-full" placeholder="First name"
                            :class="{ 'p-invalid': errorMessage }"
                          />
                          <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
                            {{ errorMessage }}
                          </Message>
                        </Field>
                      </div>

                      <div class="field">
                        <Field
                          v-slot="{ field, errorMessage }" v-model="model.card.billingAddress.last_name"
                          name="billingLastName"
                        >
                          <InputText
                            v-bind="field" id="lastName" class="w-full" placeholder="Last name"
                            :class="{ 'p-invalid': errorMessage }"
                          />
                          <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
                            {{ errorMessage }}
                          </Message>
                        </Field>
                      </div>
                    </div>

                    <!-- Company (optional) -->
                    <div class="field mb-4">
                      <Field
                        v-slot="{ field, errorMessage, handleChange }" v-model="model.card.billingAddress.company"
                        name="billingCompany"
                      >
                        <InputText
                          id="company" v-model="field.value" class="w-full" placeholder="Company (optional)"
                          :class="{ 'p-invalid': errorMessage }" @value-change="handleChange"
                        />
                        <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
                          {{ errorMessage }}
                        </Message>
                      </Field>
                    </div>

                    <!-- Address -->
                    <div class="field mb-4">
                      <Field
                        v-slot="{ field, errorMessage }" v-model="model.card.billingAddress.address_line1"
                        name="billingAddress"
                      >
                        <InputText
                          v-bind="field" id="address" class="w-full" placeholder="Address"
                          :class="{ 'p-invalid': errorMessage }"
                        />
                        <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
                          {{ errorMessage }}
                        </Message>
                      </Field>
                    </div>

                    <!-- Address line 2 (optional) -->
                    <div class="field mb-4">
                      <Field
                        v-slot="{ field }" v-model="model.card.billingAddress.address_line2"
                        name="billingAddressLine2"
                      >
                        <InputText
                          v-bind="field" id="addressLine2" class="w-full"
                          placeholder="Address line 2 (optional)"
                        />
                      </Field>
                    </div>

                    <!-- City, State, Postcode -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                      <div class="field">
                        <Field
                          v-slot="{ field, errorMessage, handleChange }" v-model="model.card.billingAddress.city"
                          name="billingCity"
                        >
                          <InputText
                            id="city" v-model="field.value" class="w-full" placeholder="City"
                            :class="{ 'p-invalid': errorMessage }" @value-change="handleChange"
                          />
                          <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
                            {{ errorMessage }}
                          </Message>
                        </Field>
                      </div>

                      <div class="field">
                        <Field
                          v-slot="{ field, errorMessage, handleChange }" v-model="model.card.billingAddress.state"
                          name="billingState"
                        >
                          <Select
                            id="state" class="w-full" :model-value="field.value" :options="stateOptions"
                            option-label="name" option-value="name" placeholder="Select state"
                            :loading="isGetCountryLoading" :disabled="!model.card.billingAddress.country" filter
                            @value-change="handleChange"
                          />
                          <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
                            {{ errorMessage }}
                          </Message>
                        </Field>
                      </div>

                      <div class="field">
                        <Field
                          v-slot="{ field, errorMessage, handleChange }"
                          v-model="model.card.billingAddress.postal_code" name="billingPostcode"
                        >
                          <InputText
                            id="postcode" v-model="field.value" class="w-full" placeholder="Postcode"
                            :class="{ 'p-invalid': errorMessage }" @value-change="handleChange"
                          />
                          <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
                            {{ errorMessage }}
                          </Message>
                        </Field>
                      </div>
                    </div>

                    <!-- Phone -->
                    <div class="field mb-4">
                      <Field
                        v-slot="{ field, errorMessage, handleChange }" v-model="model.card.billingAddress.phone"
                        name="billingPhone"
                      >
                        <InputText
                          id="phone" v-model="field.value" class="w-full" placeholder="Phone (required)"
                          :class="{ 'p-invalid': errorMessage }" @value-change="handleChange"
                        />
                        <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
                          {{ errorMessage }}
                        </Message>
                      </Field>
                    </div>

                    <!-- email -->
                    <Field
                      v-slot="{ field, errorMessage, handleChange }" v-model="model.card.email"
                      name="billingEmail"
                    >
                      <div class="field mb-4">
                        <InputText
                          v-model="field.value" placeholder="Billing Email" class="w-full"
                          :class="{ 'p-invalid': errorMessage }" @value-change="handleChange"
                        />
                        <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
                          {{ errorMessage }}
                        </Message>
                      </div>
                    </Field>
                  </div>
                </template>

                <Field
                  v-slot="{ errorMessage }" v-model="model.is_default_payment_method"
                  name="is_default_payment_method" as="div" class="form-item is-required"
                >
                  <div class="form-item__content flex flex-col gap-y-2 my-2">
                    <div class="flex items-center gap-x-2">
                      <RadioButton
                        v-model="model.is_default_payment_method"
                        input-id="is_default_payment_method_primary" name="is_default_payment_method" :value="true"
                      />
                      <label for="is_default_payment_method_primary" class="cursor-pointer">Make Default
                        <strong>Primary</strong>
                        Payment Method?</label>
                      <RadioButton
                        v-model="model.is_default_payment_method" class="ml-4"
                        input-id="is_default_payment_method_secondary" name="is_default_payment_method"
                        :value="false"
                      />
                      <label for="is_default_payment_method_secondary" class="cursor-pointer">Make Default
                        <strong>Secondary</strong> Payment Method?</label>
                    </div>
                    <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
                      {{ errorMessage }}
                    </Message>
                    <p class="text-sm text-gray-500 my-2">
                      This will not affect any current active schedules.
                      Your selected preference will be used to
                      autofill future
                      registrations.
                    </p>
                  </div>
                </Field>

                <div class="flex items-center gap-x-2">
                  <Checkbox v-model="model.terms" binary input-id="terms" />
                  <label for="terms" class="text-sm">
                    By ticking, you are confirming that you have read, understood andd agree to the
                    <span class="font-bold cursor-pointer" @click.stop="showTerms = true">
                      Terms and Conditions
                    </span>
                  </label>
                </div>

                <!-- <div class="form-item_checkbox flex items-center gap-x-2">
                  <Checkbox v-model="model.notice" :true-value="true" :false-value="false" binary input-id="notice" />
                  <label for="notice" class="text-sm">
                    Optional: Receive your future rates notices via email and store them electronically.
                  </label>
                </div> -->
              </VeeForm>
            </div>
          </div>
        </template>
        <template v-if="currentStep === 3">
          <div class="flex flex-col items-center justify-center gap-y-4 py-12 mt-12">
            <ProgressSpinner stroke-width="4" />
            <span class="text-lg text-gray-700 font-medium">Registering, please wait...</span>
          </div>
        </template>
      </div>

      <div class="register-form-actions">
        <Button v-if="currentStep === 1" class="btn" severity="primary" label="BACK" @click="$router.back()" />
        <Button v-if="currentStep === 2" :loading="loadings.submit" class="btn" label="BACK" severity="primary" @click="handleBackStep" />

        <Button
          v-if="currentStep === 2" :loading="loadings.submit" class="btn" label="SUBMIT" severity="warn"
          @click="handleSubmit"
        />
        <Button
          v-if="currentStep === 1" class="btn" :loading="loadings.getRegisterAccountInfo" label="NEXT" severity="warn"
          @click="handleNextStep"
        />
      </div>
    </div>
    <TermsAndConditions v-model="showTerms" />

    <BottomInfo />
  </div>
</template>

<style lang="scss" scoped>
.register-page {
  position: relative;
  min-height: 100vh;
  background-color: #0073CF;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 100px 0;

  :deep(.bottom-info-content) {
    color: var(--color-white);
  }
}

.register-form-container {
  max-width: 40vw;
  margin: 0 auto;
  padding: 20px;
  display: flex;
  justify-content: center;
  align-items: center;

  h1 {
    margin-bottom: 20px;
  }
}

.register-form {
  margin: 0 auto 0 200px;
  width: 100%;
  max-width: 40vw;
  background-color: var(--color-white);
  border-radius: 24px;
  padding: 20px;
  padding-bottom: 50px;

  &.show-payment-method {
    max-width: 80vw;
    margin: 0 auto;
  }
}

.register-form-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
  padding: 0 40px;

  .btn {
    height: 38px;
    width: 230px;
    border-radius: 12px;
  }
}

.register-form-content {
  padding: 20px 30px 0 30px;
}

.register-form-step {
  padding: 0;
}

.form-item {
  width: 100%;
  flex: 1;
  display: flex;

  .form-item__label {
    width: 210px;
    display: flex;
    align-items: center;
    position: relative;
    font-size: 16px;
    color: var(--color-gray-500);
    font-weight: 600;

    &::after {
      // position: absolute;
      // left: -14px;
      content: '';
      width: 14px;
      height: 14px;
    }

    &.is-required {
      &::after {

        content: '*';
        color: var(--color-blue-500);
      }
    }
  }

  .form-item__content {
    width: 100%;
    display: flex;
    flex: 1;
  }
}

.payment-frequency {
  h2 {
    color: var(--color-gray-500);
  }

  .payment-frequency-content {
    border: 1px solid var(--color-gray-500);
    border-radius: 8px;
    padding: 8px 16px;

    strong,
    p {
      color: var(--color-gray-500);
    }
  }
}

.form-item_checkbox {
  label {
    color: var(--color-gray-500);
  }
}
</style>
