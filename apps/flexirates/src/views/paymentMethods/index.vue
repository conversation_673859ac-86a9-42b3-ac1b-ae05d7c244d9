<script setup lang="ts">
import { toTypedSchema } from '@vee-validate/yup'
import Skeleton from 'primevue/skeleton'
import { Field, Form as VeeForm } from 'vee-validate'
import { ref } from 'vue'
import * as yup from 'yup'
import { useListRefresh } from '@/composables/useListRefresh'
import { useRequestList } from '@/composables/useRequestList'
import { home as homeApi, paymentMethod, transactions as transactionsApi } from '@/services/flexirates'
import Properties from './components/properties.vue'

defineOptions({
  name: 'flexiratesPaymentMethodList',
})

const requestList = useRequestList<Api.PaymentMethodInfo, Api.FlexiratesGetCardListReq>({
  requestFn: paymentMethod.getList,
})

const paymentMethodOptions = ref()

const editPaymentMethodVisible = ref(false)

const paymentLoading = ref(false)

const currentPropertyId = ref<number | null>(null)

const {
  list = [],
  loading,
  refresh,
} = requestList

useListRefresh('flexiratesPaymentMethodList', refresh)

const showAddPaymentDialog = ref(false)

const editPaymentForm = ref<{
  levelPaymentMethod: boolean
  paymentMethod: number | string | null
  id: number | null
}>({
  levelPaymentMethod: true,
  paymentMethod: null,
  id: null,
})

const schema = toTypedSchema(yup.object({
  levelPaymentMethod: yup.boolean(),
  paymentMethod: yup.mixed<number | string>()
    .test(
      'is-valid-payment-method',
      'Payment method is required',
      (value) => {
        // 如果是数字类型且大于等于 0，则有效
        return typeof value === 'number' && !Number.isNaN(value)
        // 其他情况都失败（如空字符串、null、undefined）
      },
    )
    .required('Payment method is required'),
}))

const editPaymentMethod = (id: number) => {
  editPaymentForm.value.id = id
  editPaymentMethodVisible.value = true
}

const onChangePaymentMethod = async (values: any) => {
  try {
    paymentLoading.value = true
    const sendData = {
      id: editPaymentForm.value.id as number,
      payment_method_id: values.paymentMethod,
      payment_method_type: values.levelPaymentMethod ? 1 : 2,
    }
    const res = await homeApi.changePaymentMethod(sendData)
    if (res.code === 0) {
      editPaymentMethodVisible.value = false
      window.$toast.add({
        severity: 'success',
        summary: 'Success',
        detail: 'Change payment method successful',
      })
      refresh()
      editPaymentForm.value = {
        levelPaymentMethod: true,
        paymentMethod: null,
        id: null,
      }
    }
  }
  catch (error) {
    console.log(error)
  }
  finally {
    paymentLoading.value = false
  }
}

const getAllPaymentMethodList = async () => {
  const res = await transactionsApi.getAllAccount()
  paymentMethodOptions.value = res.data.map((item) => {
    return {
      label: item.payment_method,
      value: item.id,
    }
  })
  paymentMethodOptions.value.push({ label: 'Add New Payment Method', value: '' })
}

const handleSelectChange = (event: number | string) => {
  if (typeof event === 'string' && event === '') {
    showAddPaymentDialog.value = true
  }
}

const handleDialogClose = () => {
  getAllPaymentMethodList()
  showAddPaymentDialog.value = false
  refresh()
}

getAllPaymentMethodList()
</script>

<template>
  <div class="payment-methods">
    <!-- Loading Skeleton -->
    <template v-if="loading">
      <div v-for="n in 3" :key="n" class="skeleton-card">
        <div class="skeleton-header">
          <div class="skeleton-title-section">
            <Skeleton width="300px" height="32px" class="mb-2" />
            <Skeleton width="200px" height="16px" class="mb-1" />
            <Skeleton width="250px" height="16px" />
          </div>
          <Skeleton width="180px" height="38px" border-radius="8px" />
        </div>
        <div class="skeleton-table">
          <div class="skeleton-table-header">
            <Skeleton width="60px" height="20px" />
            <Skeleton width="80px" height="20px" />
            <Skeleton width="120px" height="20px" />
            <Skeleton width="100px" height="20px" />
            <Skeleton width="80px" height="20px" />
            <Skeleton width="100px" height="20px" />
            <Skeleton width="120px" height="20px" />
            <Skeleton width="80px" height="20px" />
          </div>
          <div v-for="row in 2" :key="row" class="skeleton-table-row">
            <Skeleton width="40px" height="28px" border-radius="4px" />
            <Skeleton width="60px" height="20px" />
            <Skeleton width="80px" height="20px" />
            <Skeleton width="60px" height="20px" />
            <Skeleton width="60px" height="20px" />
            <Skeleton width="80px" height="20px" />
            <Skeleton width="100px" height="20px" />
            <Skeleton width="80px" height="20px" />
          </div>
        </div>
      </div>
    </template>

    <!-- Actual Content -->
    <template v-else>
      <Properties
        v-for="(item, index) in list" :key="item.id" :property="item" :index="index + 1" @refresh="refresh" @edit-payment-method="editPaymentMethod"
        @add-card="(propertyId) => {
          editPaymentForm.levelPaymentMethod = false
          showAddPaymentDialog = true
          currentPropertyId = propertyId
        }"
      />
    </template>

    <CustomDialog
      title="Edit Payment Method" :visible="editPaymentMethodVisible"
      @update:visible="(val) => (editPaymentMethodVisible = val)"
    >
      <template #content>
        <VeeForm :validation-schema="schema" :initial-values="editPaymentForm" @submit="onChangePaymentMethod">
          <Field v-slot="{ field }" v-model="editPaymentForm.levelPaymentMethod" as="div" name="levelPaymentMethod">
            <div class="flex flex-wrap gap-14 py-6">
              <div class="flex items-center gap-2">
                <RadioButton v-model="field.value" input-id="primary" name="levelPaymentMethod" :value="true" />
                <label for="primary">Change Primary Payment Method</label>
              </div>
              <div class="flex items-center gap-2">
                <RadioButton v-model="field.value" input-id="secondary" name="levelPaymentMethod" :value="false" />
                <label for="secondary">Change Secondary Payment Method</label>
              </div>
            </div>
          </Field>
          <Field
            v-slot="{ field, errorMessage, handleChange }" v-model="editPaymentForm.paymentMethod" as="div"
            name="paymentMethod"
          >
            <div class="flex flex-col gap-6 mb-6">
              <label class="font-semibold">Select an existing Payment Method to change to : </label>
              <Select
                v-model="field.value" :options="paymentMethodOptions" option-label="label"
                option-value="value" placeholder="Select a Payment Method" class="w-full"
                @value-change="(e: number | string) => {
                  handleChange(e)
                  handleSelectChange(e)
                }"
              />
            </div>
            <Message v-if="errorMessage" severity="error" variant="simple" class="mb-2 -mt-4">
              {{ errorMessage }}
            </Message>
          </Field>

          <div class="edit-tips mb-6">
            <p>
              You can only select from existing payment methods.<br>
              To add a new one, go to the Payment Methods section.
            </p>
          </div>
          <div class="flex justify-end">
            <Button label="CONFIRM" severity="warn" class="btn tips-btn" type="submit" :loading="paymentLoading" />
          </div>
        </VeeForm>
      </template>
    </CustomDialog>
    <AddCardOrBank
      v-if="showAddPaymentDialog" v-model:visible="showAddPaymentDialog" :other-data="{
        property_id: currentPropertyId as number,
      }" :show-radio="true" @close="handleDialogClose"
    />
  </div>
</template>

<style lang="scss" scoped>
@use '@/styles/mixins/breakpoints.scss' as *;

.payment-methods {
  gap: 20px;
  display: flex;
  flex-direction: column;

  .payment-methods-header {
    background-color: var(--color-white);
    border-radius: 16px;
    padding: 24px 26px;

    .payment-methods-title {
      font-size: 26px;
      font-weight: 800;
      margin: 0;
      color: var(--color-gray-500-1);
    }
  }

  // Skeleton loading styles
  .skeleton-card {
    background: var(--color-white);
    border-radius: 16px;
    padding: 24px 30px;
    margin-bottom: 20px;

    .skeleton-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 24px;

      .skeleton-title-section {
        flex: 1;
      }
    }

    .skeleton-table {
      .skeleton-table-header {
        display: flex;
        gap: 16px;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid #e5e7eb;
        margin-bottom: 12px;
      }

      .skeleton-table-row {
        display: flex;
        gap: 16px;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid #f3f4f6;

        &:last-child {
          border-bottom: none;
        }
      }
    }

    @include media-breakpoint-down(md) {
      padding: 16px 20px;

      .skeleton-header {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
      }

      .skeleton-table-header,
      .skeleton-table-row {
        flex-wrap: wrap;
        gap: 8px;
      }
    }
  }

  :deep(.card-wrapper) {
    background: var(--color-white);
    border-radius: 16px;
    padding: 24px 30px;

    .card-title {
      font-size: 26px;
      font-weight: 800;
      margin-bottom: 0;
      color: var(--colors-primary-1);

      @include media-breakpoint-down(md) {
        font-size: 20px;
      }
    }

    .card-tips {
      color: var(--color-gray-500);
    }

    .empty-state-container {
      padding: 8px 0;
    }

    .add-btn {
      width: 154px;
      height: 38px;
      border-radius: 8px;
    }
  }
}
</style>
